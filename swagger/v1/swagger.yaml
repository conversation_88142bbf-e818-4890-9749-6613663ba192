---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/api/accounts/by_email":
    get:
      summary: Get borrower and loan account data
      description: Find account details by email address
      tags:
      - Web originations
      security:
      - bearer_token: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: email
        in: query
        required: true
        schema:
          type: string
      responses:
        '404':
          description: Not found
        '200':
          description: Successful
  "/api/accounts/by_identity_id":
    get:
      summary: Get borrower and loan account data
      description: Find account details by borrower identity_id
      tags:
      - Web originations
      security:
      - bearer_token: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: identity_id
        in: query
        required: true
        schema:
          type: string
      responses:
        '404':
          description: Not found
        '200':
          description: Successful
  "/api/arix_onboarding/{unified_id}/onboarding_payloads":
    get:
      summary: Fetches onboarding payloads
      tags:
      - Arix Onboarding Payloads
      parameters:
      - name: unified_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: arix onboarding payloads
        '404':
          description: record not found
  "/api/arix_onboarding/{unified_id}/loan_update":
    post:
      summary: Arix loan update
      parameters:
      - name: unified_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: arix loan update
        '404':
          description: record not found
  "/api/arix_onboarding/trigger_initiation_job":
    post:
      summary: Trigger Arix Onboarding Initiation Job
      responses:
        '200':
          description: arix initiation job
        '204':
          description: arix initiation job
  "/api/arix_onboarding/{loan_id}/resubmit_documents":
    post:
      summary: Arix loan update
      parameters:
      - name: loan_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: arix resubmit_documents
  "/api/webhooks/arix":
    post:
      summary: Processes Arix webhook payload
      description: Endpoint for receiving Arix webhooks
      tags:
      - Loans
      security:
      - bearer_token: {}
      parameters: []
      responses:
        '200':
          description: Successful
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Service Error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: object
                  properties:
                    Id:
                      type: integer
                    CreateDate:
                      type: string
                    DateInserted:
                      type: string
                    LoanId:
                      type: string
                    Status:
                      type: integer
                    TimeStamp:
                      type: string
                    MPLId: xxx
                    FailedRulesReasons:
                    - RuleName: rule name
                      Rule: some string
                      Data: some string
                      Result: true
                      FailedComplianceID: 67
                  required:
                  - LoanId
              required:
              - content
  "/api/bank-accounts/add":
    post:
      summary: Adds a new bank account
      security:
      - bearer_token: []
      tags:
      - Bank Accounts
      parameters: []
      responses:
        '201':
          description: bank account created
        '400':
          description: validation errors
        '404':
          description: loan not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                  example: 65a617591a1ad1cb06156eab
                acccount_type:
                  type: string
                  example: checking
                first_name:
                  type: string
                  example: John
                last_name:
                  type: string
                  example: Doe
                routing_number:
                  type: string
                  example: '*********'
                account_number:
                  type: string
                  example: '*********'
                bank_name:
                  type: string
                  example: Bank of America
                fund_transfer_authorize:
                  type: string
                  example: 'true'
              required:
              - request_id
              - account_type
              - first_name
              - last_name
              - routing_number
              - account_number
              - bank_name
              - fund_transfer_authorize
  "/api/bank-accounts":
    post:
      summary: Adds a new bank account
      security:
      - bearer_token: []
      tags:
      - Bank Accounts
      parameters: []
      responses:
        '201':
          description: bank account created
        '400':
          description: validation errors
        '503':
          description: Error adding bank account in GDS
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                  example: 65a616a21a1ad1abfe156e93
                account_type:
                  type: string
                  example: checking
                first_name:
                  type: string
                  example: John
                last_name:
                  type: string
                  example: Doe
                routing_number:
                  type: string
                  example: '*********'
                account_number:
                  type: string
                  example: '*********'
                bank_name:
                  type: string
                  example: Bank of America
                fund_transfer_authorize:
                  type: string
                  example: 'true'
                loanId:
                  type: string
                  example: 22b64315-b738-4e48-bd12-3618897189d4
              required:
              - account_type
              - first_name
              - last_name
              - routing_number
              - account_number
              - bank_name
              - fund_transfer_authorize
  "/api/bank-accounts/ipl/{requestId}":
    put:
      summary: Processes an IPl request
      security:
      - bearer_token: []
      tags:
      - Bank Accounts
      parameters:
      - name: requestId
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: bank account updated
        '400':
          description: validation errors
        '404':
          description: loan not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                acccount_type:
                  type: string
                  example: checking
                first_name:
                  type: string
                  example: John
                last_name:
                  type: string
                  example: Doe
                routing_number:
                  type: string
                  example: '*********'
                account_number:
                  type: string
                  example: '*********'
                bank_name:
                  type: string
                  example: Bank of America
                fund_transfer_authorize:
                  type: boolean
                  example: true
              required:
              - request_id
              - account_type
              - first_name
              - last_name
              - routing_number
              - account_number
              - bank_name
              - fund_transfer_authorize
  "/api/borrower/{unified_id}":
    patch:
      summary: Updates a borrower
      security:
      - bearer_token: []
      tags:
      - Borrowers
      parameters:
      - name: unified_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: borrower updated
        '400':
          description: validations failed
        '404':
          description: Not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                address_apt:
                  type: string
                address_street:
                  type: string
                city:
                  type: string
                  required: true
                date_of_birth:
                  type: string
                  description: Date of birth of applicant in YYYY-MM-DD format. Placeholder
                    for GDS compatibility when date of birth is sent in snake case
                    format.
                DATE_OF_BIRTH:
                  type: string
                  description: 'Date of birth of applicant in YYYY-MM-DD format.Note:
                    GDS passes the parameter name in all caps'
                new_email:
                  type: string
                current_email:
                  type: string
                first_name:
                  type: string
                  required: true
                last_name:
                  type: string
                  required: true
                phone_number:
                  type: string
                zip_code:
                  type: string
                ssn:
                  type: string
                state:
                  type: string
                tcpa_accepted:
                  type: boolean
        required: true
  "/api/disclosures/credit":
    get:
      summary: Get credit disclosures
      description: 'Get credit disclosures based on specific credit report hash value

        '
      tags:
      - Credit Disclosures
      parameters:
      - name: q
        in: query
        description: Credit Disclosure Hash
        required: true
        schema:
          type: string
      responses:
        '200':
          description: credit disclosure found
        '404':
          description: loan credit disclosure hash does not exist
        '500':
          description: Error during credit disclosure rendering
  "/api/webhooks/docusign":
    post:
      summary: Receive Docusign Envelope Webhook
      description: Endpoint for receiving Docusign webhooks
      tags:
      - Loans
      parameters:
      - name: loan_id
        in: query
        required: true
        schema:
          type: string
      - name: token
        in: query
        required: true
        schema:
          type: string
      - name: webhookId
        in: query
        required: true
        schema:
          type: string
      - name: loanAgreementFilename
        in: query
        required: true
        schema:
          type: string
      - name: loanAgreementTemplate
        in: query
        required: true
        schema:
          type: string
      - name: loanAgreementVersion
        in: query
        required: true
        schema:
          type: string
      - name: cscFilename
        in: query
        schema:
          type: string
      - name: cscTemplate
        in: query
        schema:
          type: string
      - name: nocFilename
        in: query
        schema:
          type: string
      - name: nocTemplate
        in: query
        schema:
          type: string
      - name: noc2Filename
        in: query
        schema:
          type: string
      - name: noc2Template
        in: query
        schema:
          type: string
      responses:
        '200':
          description: for onboarded loan ignored successfully
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
  "/api/landing_leads":
    post:
      summary: Creates a landing lead
      description: Save lead details coming from landing page
      parameters: []
      responses:
        '201':
          description: Successful
        '400':
          description: Bad request
        '428':
          description: Precondition Required
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  example: John
                last_name:
                  type: string
                  example: Doe
                phone_number:
                  type: string
                  example: '*********0'
                email:
                  type: string
                  example: <EMAIL>
                privacy_accepted:
                  type: boolean
                  example: 'true'
                tcpa_accepted:
                  type: boolean
                  example: 'true'
                url:
                  type: string
                  description: URL of the front-end page the customer visited (path
                    and query params)
                  example: "/graduation-loan?s=bf&offer=N5SQsg&utm_source=email&utm_content=day1_email"
              required:
              - url
  "/api/leads/ipl/{code}":
    get:
      summary: Find lead and start loan application
      security:
      - bearer_token: []
      description: Called by GDS to look up a lead by offer code and phone number
      tags:
      - IPL
      operationId: startLoanApplication
      parameters:
      - name: code
        in: path
        description: offer code
        required: true
        schema:
          type: string
      - name: Phone_number
        in: query
        description: phone number
        schema:
          type: string
      responses:
        '200':
          description: Successful response
        '404':
          description: Not found
        '400':
          description: missing token
        '401':
          description: external app doesn't exist
        '403':
          description: wrong app type in token
  "/api/application/pi1":
    post:
      summary: Allows spouse information to be included
      description: Updates additional info with spousal information when provided
      parameters:
      - name: X-Forwarded-For
        in: header
        schema:
          type: string
      - name: Authorization
        in: header
        required: false
        schema:
          type: string
      security:
      - bearer_token: []
      responses:
        '201':
          description: Successful
        '400':
          description: Bad Request
        '500':
          description: Bad Request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                borrower:
                  type: object
                  properties:
                    address_apt:
                      type: string
                      example: Suite 1000
                    address_street:
                      type: string
                      example: 123 Main St
                    city:
                      type: string
                      example: Chicago
                    date_of_birth:
                      type: string
                      example: '1980-10-26'
                      description: Deprecated.  Please send as part of PI2.
                    password:
                      type: string
                      example: Passsword1!
                      required: false
                      description: A Password with at least 8 characters, 1 numeric
                        , 1 lowercase, 1 uppercase and 1 special character is required
                    ssn:
                      type: string
                      example: '*********'
                      description: Deprecated.  Please send as part of PI2.
                    state:
                      type: string
                      example: IL
                    zip_code:
                      type: string
                      example: '60606'
                  required:
                  - address_street
                  - city
                  - date_of_birth
                  - email
                  - first_name
                  - last_name
                  - phone_number
                  - ssn
                  - state
                  - zip_code
                loan_application:
                  type: object
                  properties:
                    code:
                      type: string
                      example: ABC123
                  required:
                  - code
                spouse:
                  married:
                    type: boolean
                    example: true
                  first_name:
                    type: string
                    example: Obil
                  last_name:
                    type: string
                    example: Deab
                  address_street:
                    type: string
                    example: 1234 fifty st
                  address_apt:
                    type: string
                    example: 2b
                  city:
                    type: string
                    example: Center
                  state:
                    type: string
                    example: IL
                  zip_code:
                    type: string
                    example: '22244'
              required:
              - borrower
              - loan_application
  "/api/application/pi2":
    post:
      summary: Updates loan info
      description: Updates loan with additional borrower information
      parameters:
      - name: X-Forwarded-For
        in: header
        schema:
          type: string
      responses:
        '200':
          description: Success
        '400':
          description: Validation errors
        '404':
          description: Loan not found with given loan id
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_application:
                  type: object
                  properties:
                    id:
                      type: string
                      example: 72350b78-7e28-404a-a54f-466f46e72d12
                    date_of_birth:
                      type: string
                      example: '1980-10-26'
                    employment_annual_income:
                      type: number
                      example: 150000.0
                    employment_last_payment_date:
                      type: string
                      example: '2024-12-25'
                      description: when the customer last received their paycheck(in
                        YYYY-MM-DD format)
                    employment_pay_frequency:
                      type: string
                      example: biweekly
                      enum:
                      - weekly
                      - biweekly
                      - monthly
                      - semi_monthly
                    employment_status:
                      type: string
                      example: employed_full_time
                      enum:
                      - employed_full_time
                      - employed_part_time
                      - military
                      - not_employed
                      - self_employed
                      - retired
                      - other
                    housing_monthly_payment:
                      type: number
                      example: 500
                  required:
                  - id
                  - employment_annual_income
                  - employment_last_payment_date
                  - employment_pay_frequency
                  - employment_status
                  - housing_monthly_payment
                borrower:
                  type: object
                  properties:
                    ssn:
                      type: string
                      example: '*********'
                    date_of_birth:
                      type: string
                      example: '1980-10-24'
              required:
              - loan_application
  "/api/application/select-offer":
    post:
      summary: Records the offer selected by the borrower for a loan
      description: Marks the specified offer as selected and sync's this update into
        GDS
      parameters: []
      responses:
        '200':
          description: Successful
        '400':
          description: Bad Request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_id:
                  type: string
                  example: b089060e-a0f6-4184-837b-b62ebd489738
                offer_id:
                  type: string
                  example: 591382e2-44ba-4abd-ba38-1b4322358b3e
              required:
              - loan_id
              - offer_id
  "/api/webhooks/loanpro":
    post:
      summary: Receive Loanpro Webhook
      description: Endpoint for receiving Loanpro webhooks
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      responses:
        '204':
          description: stop socure monitoring processed successfully
        '400':
          description: Invalid params
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                event_type:
                  type: string
                  example: stamp_contract
                ids:
                  type: object
                  properties:
                    loan_id:
                      type: string
                  required:
                  - loan_id
              required:
              - event_type
              - ids
  "/api/loan/ipl/{requestId}/send-offers":
    post:
      summary: Sends email with offers
      description: 'Send offers to borrower

        '
      tags:
      - IPL
      operationId: sendOffers
      parameters:
      - name: requestId
        in: path
        required: true
      security:
      - bearer_token: []
      responses:
        '201':
          description: Send email to borrower of loan
        '400':
          description: Loan has wrong product_type
        '404':
          description: Loan does not exist
  "/api/loan/inquiry/{loanInquiryId}":
    get:
      summary: Get loan inquiry by loan inquiry ID
      tags:
      - Loans
      operationId: loanInquiry
      security:
      - bearer_token: {}
      parameters:
      - name: loanInquiryId
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: successful
        '404':
          description: Loan inquiry not found
        '405':
          description: Loan inquiry offer expired
  "/api/loan/ipl/app-by-phone":
    post:
      summary: App by phone
      security:
      - bearer_token: []
      tags:
      - Loans
      parameters: []
      responses:
        '201':
          description: successful
        '500':
          description: error happens when creating user
        '405':
          description: active loan already exists
        '404':
          description: lead could not be found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                  example: '12345678'
                borrower:
                  type: object
                  properties:
                    email:
                      type: string
                      example: <EMAIL>
                    first_name:
                      type: string
                      example: John
                    last_name:
                      type: string
                      example: Doe
                    ssn:
                      type: string
                      example: '*********'
                    address_street:
                      type: string
                      example: 123 Main St
                    address_apt:
                      type: string
                      example: Apt 1
                    city:
                      type: string
                      example: New York
                    state:
                      type: string
                      example: NY
                    zip_code:
                      type: string
                      example: '10001'
                    phone_number:
                      type: string
                      example: '*********0'
                    tcpa_accepted:
                      type: boolean
                      example: true
                loan_app:
                  type: object
                  properties:
                    code:
                      type: string
                      example: '12345678'
                    product_type:
                      type: string
                      example: IPL
                    credit_score_range:
                      type: string
                      example: good
                    loan_creation_date:
                      type: date
                      example: '2020-01-01'
  "/api/loan/ipl/{requestId}/final-decision":
    put:
      summary: Final decision
      security:
      - bearer_token: []
      tags:
      - Loans
      parameters:
      - name: requestId
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: successful
        '404':
          description: loan record could not be found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: BACK_END_DECLINED
                credit_score:
                  type: integer
                  example: 617
                decision_reason_number:
                  type: string
                  example: '2'
                decline_reason_text:
                  type: string
                  example: Unable to verify income
                decline_reasons:
                  type: array
                  example:
                  - Unable to verify income
                score_factor:
                  type: string
                  example: Serious delinquency; Proportion of balances to credit limits
                    is too high on bank revolving or other revolving accounts; Number
                    of accounts with delinquency; Length of time accounts have been
                    established
                verified_dti:
                  type: float
                  example: 0.262
              required:
              - status
  "/api/loan/dm2/{requestId}/final-decision":
    put:
      summary: UPL Final decision
      security:
      - bearer_token: []
      tags:
      - Loans
      parameters:
      - name: requestId
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: successful
        '404':
          description: loan record could not be found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: BACK_END_DECLINED
                  required: true
                  enum:
                  - BACK_END_DECLINED
                  - APPROVED
                above_rejection_data:
                  type: object
                  properties:
                    decision_reason_number:
                      type: string
                      example: '2'
                    decline_reason_text:
                      type: string
                      example: Unable to verify income
                    decline_reasons:
                      type: array
                      example:
                      - Unable to verify income
                    credit_score:
                      type: integer
                      example: 617
                    score_factor:
                      type: string
                      example: Serious delinquency; Proportion of balances to credit
                        limits is too high on bank revolving or other revolving accounts;
                        Number of accounts with delinquency; Length of time accounts
                        have been established
                    verified_dti:
                      type: float
                      example: 0.262
              required:
              - status
  "/api/loan/ipl/send-disclosure":
    post:
      summary: Sends email with information and disclosure
      description: "`first_name` AND/OR `lead_code` is required\n"
      tags:
      - IPL
      operationId: sendDisclosure
      security:
      - bearer_token: {}
      parameters: []
      responses:
        '201':
          description: Send email using lead_code
        '400':
          description: first_name is missing
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email_address:
                  type: string
                  example: <EMAIL>
                first_name:
                  type: string
                  example: John
                lead_code:
                  type: string
                  example: '87654'
              required:
              - email_address
  "/api/loan/{requestId}/withdraw":
    put:
      summary: Withdraw loan request
      tags:
      - Loans
      operationId: withdraw
      security:
      - bearer_token: {}
      parameters:
      - name: requestId
        in: path
        required: true
        schema:
          type: string
      responses:
        '201':
          description: successful
        '404':
          description: Loan or borrower not found
        '405':
          description: Loan ineligible to be withdrawn
  "/api/loan/apply":
    post:
      summary: UPL Apply for Loan
      tags:
      - Loans
      security:
      - bearer_token: {}
      parameters: []
      responses:
        '201':
          description: successful - offered
        '200':
          description: successful - declined
        '400':
          description: bad request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: number
                  example: 12591.0
                loan_purpose:
                  type: string
                  example: debt_consolidation
                first_name:
                  type: string
                  example: John
                last_name:
                  type: string
                  example: Doe
                address_street:
                  type: string
                  example: 123 Main St, Apt 3a
                city:
                  type: string
                  example: Chicago
                state_code:
                  type: string
                  example: IL
                zip_code:
                  type: string
                  example: '60606'
                phone_number:
                  type: string
                  example: '3303301234'
                email:
                  type: string
                  example: <EMAIL>
                date_of_birth:
                  type: string
                  example: 12-31-1980
                income:
                  type: number
                  example: 38400.0
                monthly_housing_payment:
                  type: number
                  example: 850.0
                ssn:
                  type: string
                  example: '*********'
                oppId:
                  type: string
                  example: 0065d0000184mMuAAI
  "/api/loan/app-from-inquiry":
    post:
      summary: App from inquiry
      tags:
      - Loans
      security:
      - bearer_token: {}
      parameters: []
      responses:
        '201':
          description: successful
        '400':
          description: bad request
        '404':
          description: Loan inquiry not found
        '503':
          description: Third party request error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                esign_consent:
                  type: boolean
                  example: true
                loan_inquiry_id:
                  type: string
                  example: 3a2f4bda-7ea3-4385-ba3f-10100d9f6c22
                password:
                  type: string
                  example: really-secure-password123
  "/api/loan/resend-onboarding-email":
    post:
      summary: Resend onboarding email
      security:
      - bearer_token: []
      tags:
      - Loans
      parameters: []
      responses:
        '200':
          description: Send onboarding email
        '404':
          description: Loan not found
        '400':
          description: request_id is missing
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                  example: eb8736e3-0000-1111-0000-111111111111
              required:
              - request_id
  "/api/loan/ipl/{loan_id}/store-signed-contracts":
    patch:
      summary: It uploads the existing signed contract documents from DocuSign to
        AWS S3.
      security:
      - bearer_token: []
      tags:
      - Loans
      parameters:
      - name: loan_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Store Signed Contracts
        '404':
          description: RecordNotFound
        '400':
          description: NOC2 S3 storage error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                ip_address:
                  type: string
                  example: 127.0.0.1
                loan_agreement_filename:
                  type: string
                  example: CRB Installment Loan Agreement_version_26_JOHN_DOE_9b8d997d-4e1c-451c-bad2-912d06c84b06.pdf
                loan_agreement_template:
                  type: string
                  example: CRB_INSTALLMENT_LOAN_AGREEMENT
                loan_agreement_version:
                  type: string
                  example: '26'
                docusign_webhook_id:
                  type: string
                  example: d192a529-c364-4e58-a41e-5254aed63c2a
                til_filename:
                  type: string
                  example: ''
                til_template_name:
                  type: string
                  example: ''
                csc_filename:
                  type: string
                  example: CREDIT_SERVICES_CONTRACT_MARYLAND_version_2_JOHN_DOE_6c0bc20a-f4f4-4278-be74-3a9cdfcac9d9.pdf
                csc_template:
                  type: string
                  example: CREDIT_SERVICES_CONTRACT_MARYLAND
                noc_filename:
                  type: string
                  example: NOTICE_OF_CANCELLATION_MARYLAND_version_2_Robert_Cumberland_a47eb96f-8aad-4200-aaa7-91d312513f80.pdf
                noc_template:
                  type: string
                  example: NOTICE_OF_CANCELLATION_MARYLAND
                noc2Template:
                  type: string
                  example: NOTICE_OF_CANCELLATION_MARYLAND_2
                noc2_filename:
                  type: string
                  example: NOTICE_OF_CANCELLATION_MARYLAND_2_version_2_JOHN_DOE_258069fc-b130-418e-8ec0-f89f15aac09f.pdf
              required:
              - loan_id
  "/api/loan/dm2/bank-account/update":
    post:
      summary: Update bank account
      security:
      - bearer_token: []
      tags:
      - Bank Accounts
      parameters: []
      responses:
        '201':
          description: bank account updated
        '400':
          description: validation errors
        '404':
          description: loan not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                  example: 656679cd1a1ad1b809145db7
                  description: Identifies the loan associated with the account
                acccount_type:
                  type: string
                  example: checking
                first_name:
                  type: string
                  example: John
                last_name:
                  type: string
                  example: Doe
                routing_number:
                  type: string
                  example: '*********'
                account_number:
                  type: string
                  example: '*********'
                bank_name:
                  type: string
                  example: Bank of America
                fund_transfer_authorize:
                  type: boolean
                  example: true
              required:
              - request_id
              - account_type
              - first_name
              - last_name
              - routing_number
              - account_number
              - bank_name
              - fund_transfer_authorize
  "/api/loan/{unified_id}":
    patch:
      summary: verified_income for the loan
      security:
      - bearer_token: []
      tags:
      - Loans
      parameters:
      - name: unified_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ok
        '404':
          description: loan not found
        '405':
          description: loan is not in pending state
        '400':
          description: validation errors
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                verified_income:
                  type: number
                  example: 150000.0
                verified_income_ratio:
                  type: number
                  example: 0.95
                num_hard_inquiries_60_days:
                  type: number
                  example: 2
                num_hard_inquiries_90_days:
                  type: number
                  example: 3
              required:
              - num_hard_inquiries_60_days
              - num_hard_inquiries_90_days
  "/api/loan/{requestId}/details":
    get:
      summary: Details for decisioning
      security:
      - bearer_token: []
      tags:
      - Eligibility
      parameters:
      - name: requestId
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: details found
        '404':
          description: not found
        '500':
          description: eligibility lookup fails
  "/api/oauth/token":
    post:
      summary: "/api/oauth/token"
      tags:
      - OAuth
      parameters: []
      responses:
        '200':
          description: Successfully authenticated
        '400':
          description: Bad Request
        '401':
          description: Authentication failed
      operationId: token-basic-auth
      security:
      - basic_auth: []
      description: Performs OAuth authentication specified with 'grant_type'. When
        `grant_type` = `client_credentials` you need to provide `client_id:client_secret`
        with BasicAuth
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                grant_type:
                  type: string
                  description: OAuth grant type
                  enum:
                  - client_credentials
                  example: client_credentials
              required:
              - grant_type
  "/api/webhooks/ocrolus":
    post:
      summary: Receive Ocrolus Webhook
      description: Endpoint for receiving Ocrolus webhooks
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Book Verified webhook processed successfully
        '400':
          description: Invalid params
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                event_name:
                  type: string
                  example: book.verified
                book_uuid:
                  type: string
                  example: 1e03d9cd-7d84-4672-bc3d-c19cd19c500b
              required:
              - event_name
              - book_uuid
  "/api/offers/ipl/generated":
    post:
      summary: Generate offers
      security:
      - bearer_token: []
      description: |
        Receives offers for loan from GDS.
        If offers are empty - mark loan as declined, generate PDF document and save email to borrower
      tags:
      - Offers
      operationId: generated
      parameters: []
      responses:
        '200':
          description: handles credit freeze
        '201':
          description: mark offer as declined
        '400':
          description: Missing credit file
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                app_status:
                  type: string
                decision_reason_number:
                  type: string
                decline_reason_text:
                  type: string
                decline_reasons:
                  type: array
                  items: string
                credit_score:
                  type: number
                score_factor:
                  type: string
                originating_party:
                  type: string
                offers:
                  type: array
                  items:
                    type: object
                    properties:
                      offer_id:
                        type: string
                      offer_url:
                        type: string
                      hero_offer:
                        type: boolean
                      offer_creation_date:
                        type: date
                      lender_network:
                        type: string
                      principal_loan_amount:
                        type: number
                      amount_financed:
                        type: number
                      term:
                        type: string
                      monthly_payment:
                        type: number
                      interest_rate:
                        type: number
                      origination_fee_amount:
                        type: number
                      origination_fee_percent:
                        type: number
                      total_advance_period_interest:
                        type: number
                      advanced_period_interest_per_term:
                        type: number
                      initial_term_payment:
                        type: number
                      final_term_payment:
                        type: number
                      originating_party:
                        type: string
                      settlement_amount:
                        type: number
                      cash_out_amount:
                        type: number
                      description:
                        type: string
                credit_freeze:
                  type: boolean
                credit_model_level:
                  type: string
                credit_model_score:
                  type: number
                de_decision_champion:
                  type: string
                de_decision_challenger:
                  type: string
              required:
              - request_id
              - app_status
  "/api/offers/save-selection":
    post:
      summary: Save offer selection
      security:
      - bearer_token: []
      description: 'Save selected offer

        '
      tags:
      - Offers
      parameters: []
      responses:
        '201':
          description: offer selection saved
        '400':
          description: app_status is missing
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                  example: eb8736e3-0000-1111-0000-111111111111
                offer_id:
                  type: string
                  example: 11:02:0629544.0000
                app_status:
                  type: string
                  example: OFFERED_SELECTED
                reassigned_cohort:
                  type: string
                  example: champion
              required:
              - request_id
              - offer_id
              - app_status
  "/api/offers/IPL/{loanId}":
    get:
      summary: Retrieve offer by loan id
      security:
      - bearer_token: []
      tags:
      - Offers
      parameters:
      - name: loanId
        in: path
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: should return empty array when offers previously requested
        '405':
          description: possible expired status
  "/api/webhooks/plaid":
    post:
      summary: Receive Plaid Webhook
      description: Endpoint for receiving Plaid webhooks
      parameters: []
      responses:
        '204':
          description: WEBHOOK_UPDATE_ACKNOWLEDGED webhook processed successfully
        '400':
          description: Invalid params
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                webhook_type:
                  type: string
                  example: ASSETS
                webhook_code:
                  type: string
                  example: PRODUCT_READY
              required:
              - webhook_type
              - webhook_code
  "/api/webhooks/sendgrid":
    post:
      summary: Receive SendGrid Webhook
      description: Endpoint for receiving SendGrid webhooks
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: CF-Connecting-IP
        in: header
        required: true
        schema:
          type: string
      - name: CF-Ray
        in: header
        required: true
        schema:
          type: string
      - name: X-Forwarded-For
        in: header
        required: true
        schema:
          type: string
      responses:
        '204':
          description: webhook processed successfully
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: {}
              required: []
  "/api/webhooks/socure":
    post:
      summary: Receive socure Webhook
      description: Endpoint for receiving socure webhooks
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: WatchlistNotification webhook processed successfully
        '401':
          description: Unauthorized
        '400':
          description: Unsupported webhook event
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                eventGroup:
                  type: string
                  example: WatchlistNotification
                id:
                  type: string
                origId:
                  type: string
                reason:
                  type: string
                event:
                  type: object
              required:
              - eventGroup
              - id
              - origId
              - reason
              - event
  "/api/talkdesk_events":
    post:
      summary: Receives talkdesk webhook events
      description: Receives talkdesk webhook events and enqueues them for processing
        via Sidekiq
      security:
      - bearer_token: {}
      parameters: []
      responses:
        '201':
          description: Successful
        '401':
          description: Unauthenticated
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    called_at:
                      type: string
                      example: 1714077344
                      description: Unix epoch integer timestamp
                    direction:
                      type: string
                      example: OUTBOUND
                      enum:
                      - INBOUND
                      - OUTBOUND
                    disposition:
                      type: string
                      example: AGL - Submitted All Documents
                      description: Talkdesk disposition after call ended
                    id:
                      type: string
                      example: 7f4888e0-e7ed-4d54-a63d-5efec34558f4
                      description: Talkdesk's record id
                    phone_number:
                      type: string
                      example: "+15556667777"
                      description: Phone number, including country code, that was
                        dialed or that called inbound
                    talk_time:
                      type: number
                      example: 200
                      description: Duration of the call in seconds
                  required:
                  - called_at
                  - direction
                  - disposition
                  - id
                  - phone_number
                  - talk_time
              required:
              - data
  "/api/todos/sync-tasks":
    post:
      summary: Sync tasks
      security:
      - bearer_token: []
      tags:
      - Todos
      parameters: []
      responses:
        '201':
          description: sync tasks success
        '400':
          description: validation errors
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                  example: 65a616a21a1ad1abfe156e93
                task_id:
                  type: string
                  example: f449cc39-d570-48f7-92c5-06fa5ed35f90
                type:
                  type: string
                  example: payment_adherence
                documents:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        example: 01de2ce0-5f57-46ac-a98d-83d82c624022
                      type:
                        type: string
                        example: application/pdf
                      status:
                        type: string
                        example: approved
                      url:
                        type: string
                        example: https://example.com
                      name:
                        type: string
                        example: file.pdf
                      rejected_reason:
                        type: string
                        example: rejected reason
  "/api/todos/trigger-resync":
    post:
      summary: Trigger resync
      security:
      - bearer_token: []
      tags:
      - Todos
      parameters: []
      responses:
        '201':
          description: sync tasks success
        '400':
          description: validation errors
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                request_id:
                  type: string
                  example: 65a616a21a1ad1abfe156e93
  "/api/todos/documents/{documentId}":
    get:
      summary: Download a document
      security:
      - bearer_token: []
      tags:
      - Todos
      parameters:
      - name: documentId
        in: path
        description: Document ID
        required: true
        schema:
          type: string
      responses:
        '200':
          description: document found
        '404':
          description: document not found
        '500':
          description: s3 error
  "/api/todos/agent-upload-completed/{request_id}":
    post:
      summary: Send a agent uploaded bank doc to ocrolus
      security:
      - bearer_token: []
      tags:
      - Todos
      parameters:
      - name: request_id
        in: path
        description: Request id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: agent upload completed
        '404':
          description: loan todo bank does not exist
  "/api/users/update_email":
    post:
      summary: Updates the email assigned to a user account
      security:
      - bearer_token: []
      tags:
      - Users
      parameters: []
      responses:
        '204':
          description: successfully updates the email address on the specified user
            account
        '400':
          description: no updated email is specified
        '422':
          description: email already in use is specified
        '404':
          description: user not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                current_email:
                  type: string
                  example: <EMAIL>
                  required: true
                  description: Current email address assigned to the user's account
                updated_email:
                  type: string
                  example: <EMAIL>
                  required: true
                  description: New email address to be assigned to the user's account
  "/api/utils/deliver_noaa":
    post:
      summary: Delivers NOAA to customers
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      - NOAA
      parameters: []
      responses:
        '200':
          description: delivers NOAA for a given loan/loan-inquiry ID
        '400':
          description: when the product type is not of IPL or UPL
        '404':
          description: when loan or a loan inquiry record is not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                product_type:
                  type: string
                  example: IPL
                  enum:
                  - IPL
                  - UPL
                  required: true
                  description: Product type
                loan_id:
                  type: string
                  example: 72350b78-7e28-404a-a54f-466f46e72d12
                  description: ID of the loan for which we want to trigger NOAA, required
                    if product_type is IPL
                loan_inquiry_id:
                  type: string
                  example: 22b64315-b738-4e48-bd12-3618897189d4
                  description: ID of the loan inquiry for which we want to trigger
                    NOAA, required if product_type is UPL
  "/api/utils/sync_loan_status":
    post:
      summary: Sync loan application status to GDS for one ore more loans
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      parameters: []
      responses:
        '200':
          description: sync status job enqueued
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_ids:
                  type: array
                  example:
                  - fa4cc403-6b39-4be4-92e7-000777551ba2
                  description: Loan ids specifying loans to be synced
                request_ids:
                  type: array
                  example:
                  - 658370c11a1ad1155914ffd7
                  description: Request ids specifying loans to be synced
                unified_ids:
                  type: array
                  example:
                  - '43287932'
                  description: Unified ids specifying loans to be synced
  "/api/utils/force_loan_onboard":
    post:
      summary: Forces loan onboarding to dash or loanpro for a given unified id
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      parameters: []
      responses:
        '200':
          description: force onboard to dash or loanpro
        '400':
          description: when an unknown entity name is passed an input
        '404':
          description: when loan or a signed TIL history record not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                to_entity:
                  type: string
                  example: dash
                  description: onboard to "dash" or to "loanpro"
                  enum:
                  - dash
                  - loanpro
                unified_id:
                  type: string
                  example: '43287932'
                  description: Unified ID of a loan that needs force onboarding to
                    dash
  "/api/utils/force_socure_monitoring":
    post:
      summary: Forces socure monitoring for a give loanpro loan id
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      parameters: []
      responses:
        '200':
          description: force onboard to dash or loanpro
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_id:
                  type: string
                  example: '123'
                  description: loan ID that needs forced socure onboarding
                operation:
                  type: string
                  example: enable
                  description: enable/disable monitoring
  "/api/utils/force_crb_pre_approval":
    post:
      summary: Forces CRB pre-approval for a given loan id
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      parameters: []
      responses:
        '200':
          description: force CRB pre-approval
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_id:
                  type: string
                  example: '123'
                  description: loan ID that needs forced CRB pre-approval
  "/api/utils/force_plaid_asset_report":
    post:
      summary: Forces generating plaid asset report for a give loan id
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      parameters: []
      responses:
        '200':
          description: force generating plaid asset report
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_id:
                  type: string
                  example: '123'
                  description: loan ID that needs forced socure onboarding
  "/api/utils/generate_offer_code":
    post:
      summary: Generates a intake-page url with eligible lead code in lower environments
      tags:
      - Loans
      - Utils
      parameters: []
      responses:
        '201':
          description: returns a intake page url with eligible lead code
        '403':
          description: returns disabled endpoint message when attempted from production
        '422':
          description: returns no leads created if unable to create lead
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                source:
                  type: string
                  example: bf
                  description: Source of the lead
                  enum:
                  - bf
                  - fllg
  "/api/utils/upload_report":
    post:
      summary: Uploads documents to Arix for a given unified id
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      parameters: []
      responses:
        '201':
          description: uploads document to Arix
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                unified_id:
                  type: string
                  example: '*********0'
                  description: Unified id of the loan
                report_type:
                  type: string
                  example: SOCURE_REPORT
                  description: Document type
                file:
                  type: file
                  format: binary
                  description: Document to upload to Arix
  "/api/utils/generate_offers/{loan_id}":
    post:
      summary: Triggers the offer generation process for a loan whose origination
        process was interrupted due to maintenance mode being enabled (i.e halted
        after the completion of the initial application data collection forms (i.e.
        PI1 & PI2) but before the generation of offers for this application).
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      parameters:
      - name: loan_id
        in: path
        schema:
          type: string
        description: ID of loan record
        required: true
      responses:
        '200':
          description: Successfully Triggered
        '422':
          description: Offers Already Generated
  "/api/utils/stamp_loan_agreement":
    post:
      summary: Create a tamped version of the Loan Agreement
      security:
      - bearer_token: []
      tags:
      - Loans
      - Utils
      parameters: []
      responses:
        '201':
          description: Works with loanpro id
        '404':
          description: Loan not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_id:
                  type: string
                  example: 441371c4-cd49-43e0-9009-b1a901391fe8
                  required: true
                  description: Loan ID or Unified ID
                override_file:
                  type: boolean
                  example: false
                  description: Override existing stamped document
  "/.well-known/openid-configuration":
    get:
      summary: openid-configuration
      tags:
      - well-known
      operationId: well-known-openid-configuration
      description: Retrieves the well-known openid-configuration configuration file
      responses:
        '200':
          description: Successfully authenticated
  "/.well-known/jwks.json":
    get:
      summary: jwks-json
      tags:
      - well-known
      operationId: well-known-jwks-json
      description: Retrieves the jwks.json configuration file
      responses:
        '200':
          description: Successfully authenticated
  "/api/validate_email":
    post:
      summary: Checks for Valid Email
      description: Checks for valid emails
      tags:
      - Loans
      security:
      - bearer_token: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: OK
        '400':
          description: Bad request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: <EMAIL>
                source:
                  type: string
                  example: CaseCenter Test
              required:
              - email
              - source
components:
  securitySchemes:
    basic_auth:
      description: Basic auth for usage in when obtaining a oauth token with the `client_credentials`
        flow. Use `client_id:client_secret` for this authentication.
      type: http
      scheme: basic
    bearer_token:
      description: Oauth bearer token
      type: http
      scheme: bearer
      bearerFormat: JWT
servers:
- url: http://localhost:3002
  variables:
    defaultHost:
      default: localhost:3002
