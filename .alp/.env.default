##########################################################################################
# Above Lending Platform
#
# This file contains default environment variables to setup docke environment.
##########################################################################################

# Frontend development
VITE_RUBY_HOST=vite
__VITE_ADDITIONAL_SERVER_ALLOWED_HOSTS=vite

# AURORA Database
DATABASE_HOST=host.docker.internal
ABOVELENDING_DATABASE_PASSWORD=abovedev

# Redis for Rails Cache Store / Flipper
REDIS_PREFIX=ams
REDIS_URI=redis://host.docker.internal:6379/0

# Redis for Sidekiq / ActionCable
REDIS_URL=redis://host.docker.internal:6379/1

# coverband
COVERBAND_DISABLE_AUTO_START=true
