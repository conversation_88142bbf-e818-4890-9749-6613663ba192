# frozen_string_literal: true

module Clients
  class CrbApi
    class ApplicationError < StandardError; end
    class InvalidResponse < StandardError; end
    class RequestFailure < Clients::Errors::Faraday; end

    class << self
      ARIX_AUTH_HEADER = 'arix_authorization_header'

      def pre_approval(request_body)
        response = api_conn.post(
          "/preapproval/api/v2/applications/#{config.partner_schema_id}/dryrun",
          request_body.to_json
        )
        record_event(request_body: request_body, response_body: response.body)
        parse_response(response)
      rescue StandardError => e
        handle_error(request_body: request_body, exception: e)
      end

      private

      def config
        Rails.application.config_for(:crb)
      end

      def api_conn
        @api_conn = Faraday.new(url: config.api_base_url,
                                headers: {
                                  'Content-Type': 'application/json',
                                  Accept: 'application/json',
                                  Authorization: authorization_header
                                }) do |f|
          # Enable multipart middleware to support package upload requests
          f.request :multipart
          f.use Middleware::CfRayForwarder
        end
      end

      def auth_conn
        return @auth_conn if defined? @auth_conn

        auth_conn = Faraday.new(
          url: config.auth_base_url,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json'
          }
        ) do |f|
          f.use Middleware::CfRayForwarder
        end

        auth_conn.set_basic_auth(config.client_id, config.client_secret)

        @auth_conn = auth_conn
      end

      def authorization_header
        auth_token = fetch_auth_token
        "#{auth_token['token_type']} #{auth_token['access_token']}"
      end

      def fetch_auth_token
        request_body = { grant_type: 'client_credentials' }
        response = auth_conn.post('/connect/token', URI.encode_www_form(request_body))
        JSON.parse(response.body)
      end

      def parse_response(response)
        return JSON.parse(response.body) if response.success?

        raise InvalidResponse, "Invalid response from CRB API: #{response.status} - #{response.body}"
      end

      def handle_error(request_body:, exception:)
        event_name = "crb_#{caller_locations(1, 1).first.base_label}"
        Rails.logger.error("CrbApi - Request failed with exception: #{exception.message}", exception)
        RecordApiEvent.call(event_name:, request_body:, response: { body: exception.message })
        raise RequestFailure, exception.message
      end

      def record_event(request_body:, response_body:)
        event_name = "crb_#{caller_locations(1, 1).first.base_label}"
        RecordApiEvent.call(event_name:, request_body:, response: { body: response_body })
      end
    end
  end
end
