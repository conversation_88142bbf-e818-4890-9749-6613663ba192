# frozen_string_literal: true

module Underwriting
  class InformativeResearch < Base
    attribute :IR_Age_of_the_Oldest_Tradeline, :string
    attribute :IR_Consumer_Deceased_Ind, :string
    attribute :IR_CreditFile_ResultStatusType, :string
    attribute :IR_CreditHistory_LessThan2Years_Ind_DM12, :string
    attribute :IR_CreditHistory_LessThan3Years_Ind, :string
    attribute :IR_CreditHistory_LessThan5Years_Ind_APL12, :string
    attribute :IR_CreditHistory_LessThan7Years_Ind_DM12, :string
    attribute :IR_CreditHistoryLessThan5Years_Ind, :string
    attribute :IR_Defferment_Plan_Mortgage_or_Auto_Ind, :string
    attribute :IR_Ever_Had_Above_Loan_Indicator, :string
    attribute :IR_FICO_Beacon5, :string
    attribute :IR_Fico5ReasonDescription1, :string
    attribute :IR_Fico5ReasonDescription2, :string
    attribute :IR_Fico5ReasonDescription3, :string
    attribute :IR_Fico5ReasonDescription4, :string
    attribute :IR_FrozenFile_Ind, :string
    attribute :IR_Max_Utilization_Of_Bank_Card, :string
    attribute :IR_NoHit_Ind, :string
    attribute :IR_Num_60DaysPastdue_DM14, :string
    attribute :IR_Num_ActiveTradelines_180Days_NonDelinquent, :string
    attribute :IR_NUM_BANKRUPTCY_CH_7_EVER, :integer
    attribute :IR_NUM_BANKRUPTCY_CH_7, :integer
    attribute :IR_Num_Bankruptcy_DM11, :string
    attribute :IR_Num_Bankruptcy_UPL11, :string
    attribute :IR_Num_ChargeOffers_Within36Months_UPL17, :string
    attribute :IR_Num_ChargeOffers_Within6Months_DM17, :string
    attribute :IR_NUM_CONSUMER_DISPUTE_INDICATOR, :integer
    attribute :IR_Num_Credit_Cards_730D_Pre_Beyond, :string
    attribute :IR_Num_DerogatoryTrades_DM13, :string
    attribute :IR_Num_DischargedPetitionedBankruptcy_Within7Years, :string
    attribute :IR_num_hard_inquiries_90_days, :string
    attribute :IR_NUM_INQUIRIES_120_DAYS, :integer
    attribute :IR_Num_Inquiries_365Days, :string
    attribute :IR_Num_Inquiries_60Days, :string
    attribute :IR_Num_Installment_or_Revolving_3Months, :string
    attribute :IR_NUM_OPEN_MORTGAGE, :integer
    attribute :IR_num_personal_loan_hard_inquiries_120_days, :string
    attribute :IR_num_personal_loan_hard_inquiries_60_days, :string
    attribute :IR_Num_PetitionedBankruptcy, :string
    attribute :IR_Num_RecentAutoDelinquency_3Months, :string
    attribute :IR_Num_RecentAutoDeliquency_6Months, :string
    attribute :IR_Num_RecentAutoLoans_Within180Days, :string
    attribute :IR_Num_RecentBankruptcy_Within7Years, :string
    attribute :IR_Num_RecentDelinquency_UPL14, :string
    attribute :IR_Num_RecentInquiries_180Days, :string
    attribute :IR_Num_RecentInquiries_LessThan6Months_UPL16, :string
    attribute :IR_Num_RecentInstallmentTrades_Within180Days, :string
    attribute :IR_Num_RecentMortgageDelinquency_3Months, :string
    attribute :IR_Num_RecentMortgageDeliquency_6Months, :string
    attribute :IR_Num_RecentRetailTrades_Within180Days, :string
    attribute :IR_Num_RecentRevolving_or_InstallmentInquiries_3Months, :string
    attribute :IR_Num_RecentRevolvingTradelines_Within180Days, :string
    attribute :IR_Num_RecentStudentLoans_Within180Days, :string
    attribute :IR_Num_Reposession_or_Forclosure_WithinLast7Years, :string
    attribute :IR_Num_Reposession_or_Foreclosure_Last12Months, :string
    attribute :IR_Num_TradesOpenedWithin180Days, :string
    attribute :IR_NUMBER_OF_CREDIT_CARD_LAST_180_DAYS, :string
    attribute :IR_NUMBER_OF_PERSONAL_LOAN_LAST_180_DAYS, :string
    attribute :IR_PCT_OPENED_OF_EVER_PAST_12MOS_PRE_BEYOND, :string
    attribute :IR_Settled_trades_paid_charge_Offs_Paid_Collections_Before_DRP_Enrollment, :string
    attribute :IR_Sum_Installment_Tradelines_MonthlyPayments_DM19, :string
    attribute :IR_Sum_Mortgage_Tradelines_MonthlyPayments_DM19, :string
    attribute :IR_Sum_Of_Installment_Tradelines, :string
    attribute :IR_Sum_Of_Mortgage_Monthly_Payments, :string
    attribute :IR_Sum_Of_Revolving_Tradelines, :string
    attribute :IR_Sum_RevolvingCreditLimitAmount_DM15, :string
    attribute :IR_Sum_RevolvingTradelines_MonthlyPayments_DM19, :string
    attribute :IR_Sum_RevolvingUpaidBalanceAmount_DM15, :string
    attribute :IR_Tradelines_Opened_Last_Year_Ratio, :string
    attribute :IR_TRADES_OPENED_730_DAYS_PIL_ADJUSTED, :string
    attribute :IR_Unsecured_Trades_Opened_Last_Year_Ratio, :string

    def address_discrepancy_flag
      self.IR_CreditFile_ResultStatusType == 'FileReturned'
    end

    def before_drp_settled_paid_charge_off_paid_collections
      safe_to_i(self.IR_Settled_trades_paid_charge_Offs_Paid_Collections_Before_DRP_Enrollment)
    end

    def cc_utilization
      safe_to_f(self.IR_Max_Utilization_Of_Bank_Card)
    end

    def chapter_7_bankruptcies_ever
      safe_to_i(self.IR_NUM_BANKRUPTCY_CH_7_EVER)
    end

    def credit_cards_730_days
      safe_to_i(self.IR_Num_Credit_Cards_730D_Pre_Beyond)
    end

    def credit_score
      safe_to_i(self.IR_FICO_Beacon5)
    end

    def credit_freeze
      self.IR_FrozenFile_Ind == 'Y'
    end

    def deceased
      self.IR_Consumer_Deceased_Ind == 'Y'
    end

    def ever_had_above_loan_indicator
      self.IR_Ever_Had_Above_Loan_Indicator == 'TRUE'
    end

    def inquiries_120_days
      safe_to_i(self.IR_num_personal_loan_hard_inquiries_120_days)
    end

    def inquiries_365_days
      safe_to_i(self.IR_Num_Inquiries_365Days)
    end

    def inquiries_60_days
      safe_to_i(self.IR_Num_Inquiries_60Days)
    end

    def installment_or_revolving_3_months
      safe_to_i(self.IR_Num_Installment_or_Revolving_3Months)
    end

    def no_bureau_hit
      self.IR_NoHit_Ind == 'Y'
    end

    def num_bankruptcy_7_years
      safe_to_i(self.IR_Num_DischargedPetitionedBankruptcy_Within7Years)
    end

    def num_customer_disputes
      safe_to_i(self.IR_NUM_CONSUMER_DISPUTE_INDICATOR)
    end

    def num_mortgage_open
      safe_to_i(self.IR_NUM_OPEN_MORTGAGE)
    end

    def oldest_trade_line_days
      safe_to_i(self.IR_Age_of_the_Oldest_Tradeline)
    end

    def pct_opened_of_ever_past_12_months_pre_beyond
      safe_to_f(self.IR_PCT_OPENED_OF_EVER_PAST_12MOS_PRE_BEYOND)
    end

    def personal_trades_opened_in_last_180_days
      safe_to_i(self.IR_NUMBER_OF_PERSONAL_LOAN_LAST_180_DAYS)
    end

    def recent_auto_delinquency_3_months
      safe_to_i(self.IR_Num_RecentAutoDelinquency_3Months)
    end

    def recent_mortgage_delinquency_3_months
      safe_to_i(self.IR_Num_RecentMortgageDelinquency_3Months)
    end

    def repossession_foreclosure_last_12_months
      safe_to_i(self.IR_Num_Reposession_or_Foreclosure_Last12Months)
    end

    def trades_730_days_pil_adjusted
      safe_to_i(self.IR_TRADES_OPENED_730_DAYS_PIL_ADJUSTED)
    end

    def trades_opened_last_year_ratio
      safe_to_f(self.IR_Tradelines_Opened_Last_Year_Ratio)
    end

    def unsecured_trades_365_days_ratio
      safe_to_f(self.IR_Unsecured_Trades_Opened_Last_Year_Ratio)
    end
  end
end
