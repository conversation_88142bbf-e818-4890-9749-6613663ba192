# frozen_string_literal: true

# == Schema Information
#
# Table name: underwriting_details
#
#  id             :uuid             not null, primary key
#  data           :jsonb
#  created_at     :timestamptz      not null
#  updated_at     :timestamptz
#  gds_request_id :string(100)      not null
#  loan_id        :uuid             not null
#
# Indexes
#
#  index_underwriting_details_on_gds_request_id  (gds_request_id) UNIQUE
#  index_underwriting_details_on_loan_id         (loan_id)
#
# Foreign Keys
#
#  fk_rails_...  (loan_id => loans.id)
#
class UnderwritingDetail < ApplicationRecord
  belongs_to :loan, class_name: '::Loan', foreign_key: 'loan_id'

  # Credit Underwriting attributes
  delegate :lti, :pti, :dti,
           to: :decision

  delegate :address_discrepancy_flag,
           :before_drp_settled_paid_charge_off_paid_collections,
           :cc_utilization,
           :chapter_7_bankruptcies_ever,
           :credit_cards_730_days,
           :credit_freeze,
           :credit_score,
           :deceased,
           :ever_had_above_loan_indicator,
           :inquiries_120_days,
           :inquiries_365_days,
           :inquiries_60_days,
           :installment_or_revolving_3_months,
           :no_bureau_hit,
           :num_bankruptcy_7_years,
           :num_customer_disputes,
           :num_mortgage_open,
           :oldest_trade_line_days,
           :pct_opened_of_ever_past_12_months_pre_beyond,
           :personal_trades_opened_in_last_180_days,
           :recent_auto_delinquency_3_months,
           :recent_mortgage_delinquency_3_months,
           :repossession_foreclosure_last_12_months,
           :trades_730_days_pil_adjusted,
           :trades_opened_last_year_ratio,
           :unsecured_trades_365_days_ratio,
           to: :informative_research

  delegate :employed_full_time, :payment_shock, to: :credit_model

  delegate :program_first_application, to: :applicant

  delegate :version_policy, :version_system, to: :version

  def decision
    Underwriting::Decision.new(data.dig('decision', 'main'))
  end

  def informative_research
    Underwriting::InformativeResearch.new(data.dig('input', 'Informative Research'))
  end

  def credit_model
    Underwriting::CreditModel.new(data.dig('decision', 'Credit Model'))
  end

  def applicant
    Underwriting::Applicant.new(data.dig('input', 'Applicant'))
  end

  def version
    Underwriting::Version.new(data.dig('decision', 'Versions'))
  end

  def verified_dti
    data['verified_dti']
  end
end
