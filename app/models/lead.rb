# frozen_string_literal: true

# == Schema Information
#
# Table name: leads
#
#  id                           :uuid             not null, primary key
#  account_number               :string(255)
#  address                      :string(255)
#  cft_account_details          :string(255)
#  cft_account_holder_name      :text
#  cft_bank_name                :text
#  city                         :string(255)
#  code                         :string(50)       not null
#  code_used                    :boolean          default(FALSE)
#  data                         :json
#  date_of_birth                :date
#  deleted_at                   :timestamptz
#  email                        :string(255)
#  expiration_date              :timestamptz
#  fico_score                   :integer
#  first_name                   :string(255)      not null
#  last_name                    :string(255)      not null
#  loan_details                 :json
#  middle_name                  :string(255)
#  months_since_enrollment      :integer
#  next_payment_date            :date
#  nu_dse_account_type_c        :string(255)
#  nu_dse_bank_account_number_c :decimal(20, )    default(0)
#  nu_dse_bank_name_c           :string(255)
#  nu_dse_holder_s_name_c       :string(255)
#  nu_dse_routing_number_c      :decimal(15, )    default(0)
#  payment_details              :json
#  personal_loan_score          :integer
#  phone_number                 :string(20)
#  project_number               :string(50)
#  service_entity_name          :string(255)      default("Beyond Finance"), not null
#  ssn                          :string(11)
#  state                        :string(2)
#  suffix_name                  :string(10)
#  total_unsecured_debt         :decimal(8, 2)
#  tradeline_details            :json
#  type                         :string(100)      not null
#  utilization_unsecured_debt   :decimal(8, 2)
#  zip_4                        :string(4)
#  zip_code                     :string(5)
#  created_at                   :timestamptz      not null
#  updated_at                   :timestamptz
#  client_id                    :string(50)
#  program_id                   :string(12)
#
# Indexes
#
#  leads_code_type_unique    (code,type) UNIQUE
#  leads_code_uppercase_idx  (upper((code)::text))
#  leads_email_index         (email)
#

# The following attributes are expected to be deprecated and should not be used:
# - code_used: The presence of an in flight loan indicates that a code has been used.
class Lead < ApplicationRecord
  self.inheritance_column = nil

  TYPES = {
    PPC: 'PPC',
    DM: 'DM',
    UPL: 'UPL',
    IPL: 'IPL',
    PPC2: 'PPC2'
  }.freeze
  enum :type, TYPES

  EQUIFAX_PRODUCT_TYPES = TYPES.slice(:IPL, :UPL)

  validates :code, length: { minimum: 6, message: 'must be at least 6 characters long' }
  validates :first_name, length: { minimum: 1, message: 'cannot be empty' }
  validates :last_name, length: { minimum: 1, message: 'cannot be empty' }
  validates :phone_number, length: { is: 10, message: 'must be at least 10 digits long' }

  scope :with_phone_number, ->(phone_number) { where(phone_number:) }
  scope :with_first_name, ->(first_name) { where(arel_table[:first_name].matches("#{first_name}%")) }
  scope :with_last_name, ->(last_name) { where(arel_table[:last_name].matches("#{last_name}%")) }
  scope :with_code, ->(code) { where('UPPER(code) = UPPER(?)', code).order(created_at: :desc) }
  scope :with_program_name, lambda { |program_id|
    where(Lead.arel_table[:program_id].matches("%#{program_id}"))
  }

  def code_status
    expired? ? 'expired' : 'valid'
  end

  def expired?
    expiration_date.blank? || Time.current > expiration_date
  end

  def eligible?
    !expired?
  end
end
