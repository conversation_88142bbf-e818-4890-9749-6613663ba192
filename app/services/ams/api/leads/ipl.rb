# frozen_string_literal: true

module Ams
  module Api
    module Leads
      class Ipl < ServiceObject
        ACTIVE_STATUS_NAMES = (
          ::LoanAppStatus::ONGOING_LOAN_STATUSES + ::LoanAppStatus::POSSIBLE_EXPIRED_STATUSES
        ).uniq.freeze

        IPL = ::Loan::IPL_LOAN_PRODUCT_TYPE

        attribute :code, :string
        attribute :phone_number, :string
        validates :code, presence: true

        def call
          call_service_object do
            verify_lead
            handle_success
          end
        end

        private

        def loan
          @loan ||= ::Loan.with_code(lead.code).where(product_type: IPL).first
        end

        def lead
          @lead ||= if phone_number.present?
                      look_up_by_phone_number
                    else
                      look_up_by_code
                    end
        end

        def verify_lead
          handle_lead_not_found unless lead
          handle_lead_not_eligible if lead.expired?
          handle_active_loan_with_code if active_loan_with_code?
          handle_code_used if lead.code_used?
        end

        def handle_success
          @status = 200
          @body = LeadBlueprint.render_as_hash(lead)
        end

        def handle_lead_not_found
          raise RecordNotFound, "We're sorry. We're not able to recognize this code. Please double check your entry and try again." # rubocop:disable Layout/LineLength
        end

        def handle_lead_not_eligible
          raise RecordNotFound, "We're sorry, this code is no longer eligible."
        end

        def handle_code_used
          raise BadRequest, "We're sorry. This code has already been used."
        end

        def handle_active_loan_with_code
          raise RecordNotFound, 'There is an ongoing application with given invitation code'
        end

        def active_loan_with_code?
          return false if loan.nil?

          loan.active? && !loan.expired?
        end

        def look_up_by_phone_number
          Lead.with_code(code)
              .where(type: IPL, phone_number:)
              .first
        end

        def look_up_by_code
          Lead.with_code(code).where(type: IPL).first
        end
      end
    end
  end
end
