# frozen_string_literal: true

# rubocop:disable Metrics/ClassLength

module Crb
  module PreApprovalAttributes
    class Applicant
      attr_reader :loan, :borrower, :latest_borrower_info, :underwriting_detail, :verification_inputs

      def self.attr(...)
        new(...).attr
      end

      def initialize(loan, borrower, latest_borrower_info, underwriting_detail, verification_inputs)
        @loan = loan
        @borrower = borrower
        @latest_borrower_info = latest_borrower_info
        @underwriting_detail = underwriting_detail
        @verification_inputs = verification_inputs
      end

      def applicant_address_attributes
        {
          city: latest_borrower_info.city,
          country: 'US',
          postalCode: latest_borrower_info.zip_code,
          state: latest_borrower_info.state,
          street: latest_borrower_info.address_street,
          type: 'physical',
          unit: latest_borrower_info.address_apt
        }.compact
      end

      def applicant_basic_info_attributes
        {
          customerId: borrower.id,
          dob: borrower.date_of_birth.iso8601,
          firstName: borrower.first_name,
          lastName: borrower.last_name,
          taxId: borrower.ssn
        }
      end

      def credit_underwriting_personal_info_attributes
        {
          beforeDRPSettledPaidChargeOffPaidCollections:
          underwriting_detail.before_drp_settled_paid_charge_off_paid_collections,
          dti: underwriting_detail.dti,
          lti: underwriting_detail.lti,
          numBankruptcy7Years: underwriting_detail.num_bankruptcy_7_years,
          oldestTradeDays: underwriting_detail.oldest_trade_line_days,
          paydayLoanActivity: 1, # Stubbed
          personalTradesOpenedInLast180Days: underwriting_detail.personal_trades_opened_in_last_180_days,
          pti: underwriting_detail.pti,
          recentAcctActivity: false, # Stubbed
          repossessionForeclosureLast12Months: underwriting_detail.repossession_foreclosure_last_12_months,
          verifiedDTI: underwriting_detail.verified_dti.present? ? underwriting_detail.verified_dti.to_f : nil
        }.compact
      end

      def credit_verification_inputs_attributes
        {
          daysNegBal: verification_inputs.present? ? verification_inputs.negative_daily_balances_30_days : 0, # Stubbed
          negBalLast1Mo: verification_inputs.present? ? verification_inputs.overdrafts_30_days : 0, # Stubbed
          numNSFLast1Mo: verification_inputs.present? ? verification_inputs.nsfs_30_days : 0, # Stubbed
          recentAcctActivity: recent_account_activity
        }.compact
      end

      def credit_history_attributes
        {
          cards730Days: underwriting_detail.credit_cards_730_days,
          ccUtilization: underwriting_detail.cc_utilization,
          hadPriorAboveLoan: underwriting_detail.ever_had_above_loan_indicator,
          inquiries120Days: underwriting_detail.inquiries_120_days,
          inquiries365Days: underwriting_detail.inquiries_365_days,
          inquiries60Days: underwriting_detail.inquiries_60_days,
          recAutoDQ: underwriting_detail.recent_auto_delinquency_3_months,
          recMortDQ: underwriting_detail.recent_mortgage_delinquency_3_months,
          revInst3Months: underwriting_detail.installment_or_revolving_3_months,
          trades365DaysPreDRPRatio: underwriting_detail.pct_opened_of_ever_past_12_months_pre_beyond,
          trades365DaysRatio: underwriting_detail.trades_opened_last_year_ratio,
          trades730DaysPILAdjusted: underwriting_detail.trades_730_days_pil_adjusted,
          unsecuredTrades365DaysRatio: underwriting_detail.unsecured_trades_365_days_ratio
        }
      end

      def credit_payment_info_attributes
        {
          chapter7BankruptciesEver: underwriting_detail.chapter_7_bankruptcies_ever,
          paymentShock: underwriting_detail.payment_shock,
          enrollmentBucket: loan.loan_detail.eligibility_level,
          dscEnrollment: loan.loan_detail.months_since_enrollment.to_f,
          returnedDRPDeposits6Months: loan.loan_detail.nsfs_6_months
        }
      end

      def credit_application_info_attributes
        {
          employmentStatus: underwriting_detail.employed_full_time ? 'EMPLOYED_FULL_TIME' : 'NOT_EMPLOYED',
          isFirstApplication: underwriting_detail.program_first_application,
          numCustomerDisputes: underwriting_detail.num_customer_disputes,
          numMortgageOpen: underwriting_detail.num_mortgage_open
        }
      end

      def credit_underwriting_attributes
        credit_underwriting_personal_info_attributes.merge(credit_verification_inputs_attributes)
                                                    .merge(credit_history_attributes)
                                                    .merge(credit_payment_info_attributes)
                                                    .merge(credit_application_info_attributes)
      end

      def applicant_credit_info_attributes
        {
          underwriting: credit_underwriting_attributes,
          creditBureauScores: credit_bureau_attributes,
          modelScores: [
            {
              score: loan.loan_detail.credit_model_score,
              name: '2025_04_CHI_1753_Credit_Model_1_0',
              version: '1.0', # Stubbed
              provider: 'Above Lending',
              purpose: 'underwriting'
            }
          ]
        }.compact
      end

      def credit_bureau_attributes
        return nil if loan.loan_app_status.name == ::LoanAppStatus::FRONT_END_DECLINED_STATUS

        [
          {
            score: loan.credit_score.to_i.to_s,
            scoreVersion: 'EquifaxBeacon5.0',
            scoreDate: score_date,
            agency: 'Experian',
            reportType: 'Soft',
            # reportVersion:,
            creditFreeze: underwriting_detail.credit_freeze
          }
        ]
      end

      # def applicant_compliance_cip_scores_attributes
      # {
      # fraudScore: 0,
      # pepScore: 0,
      # nameScore: 0,
      # dobScore: 0,
      # addressScore: 0,
      # taxIdScore: 0,
      # emailScore: 0,
      # phoneScore: 0
      # }
      # end

      def applicant_compliance_credit_report_flags_attributes
        {
          # addressDiscrepancyFlag: false,
          # extendedFraudAlert: false,
          # initialFraudAlert: false,
          isDeceased: underwriting_detail.deceased,
          noBureauHit: underwriting_detail.no_bureau_hit
        }
      end

      def applicant_compliant_attributes
        {
          # compliance: {
          # vendors: [
          # {
          # verificationType: 'IDENTITY',
          # type: 'THIRD_PARTY',
          # name: 'VendorName'
          # }
          # ]
          ofac: 'PASS', # stubbed
          # mlaflag: false,
          cipScores: {}, # stubbed
          creditReportFlags: applicant_compliance_credit_report_flags_attributes
          # }
        }
      end

      def attr
        {
          addresses: [applicant_address_attributes],
          phone: [{
            type: 'Mobile',
            number: latest_borrower_info.phone_number
          }],
          email: [{
            type: 'Personal',
            email: borrower.email
          }],
          credit: applicant_credit_info_attributes,
          compliance: applicant_compliant_attributes
        }.merge(applicant_basic_info_attributes)
      end

      private

      def recent_account_activity
        return nil unless verification_inputs.present?

        verification_inputs.personal_loan_deposits || verification_inputs.high_cost_payday_cash_advance_deposits ||
          verification_inputs.high_cost_payday_cash_advance_payments
      end

      def score_date
        loan.offers.order(created_at: :desc).first.created_at.to_date.to_s
      end
    end
  end
end
# rubocop:enable Metrics/ClassLength
