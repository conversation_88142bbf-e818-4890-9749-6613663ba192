# frozen_string_literal: true

module Crb
  class Base
    class RecordNotFound < Ams::ServiceObject::RecordNotFound; end
    attr_reader :loan_id

    delegate :borrower, to: :loan
    delegate :loan_detail, to: :loan
    delegate :latest_borrower_info, to: :borrower

    def initialize(loan_id)
      @loan_id = loan_id
      validate
    end

    ############################
    # CRB Preapproval Payload Constructors #
    ############################

    def validate
      validate_onboarded_loan if onboarded_loan?
      validate_selected_offer if back_end_declined?
      validate_lead unless loan.upl?
      validate_loan_detail
      validate_latest_borrower_info
    end

    def validate_onboarded_loan
      validate_til_history
      validate_bank_account
      validate_selected_offer
    end

    def validate_bank_account
      return if bank_account.present?

      Rails.logger.error('Bank account not found', class: self.class, loan_id:)
      raise RecordNotFound, "Bank account not found for loan #{loan_id}"
    end

    def validate_lead
      return if lead.present?

      Rails.logger.error('Lead not found', class: self.class, loan_id:)
      raise RecordNotFound, "Lead not found for loan #{loan_id}"
    end

    def validate_loan_detail
      return if loan_detail.present?

      Rails.logger.error('Loan detail not found', class: self.class, loan_id:)
      raise RecordNotFound, "Loan detail not found for loan #{loan_id}"
    end

    def validate_latest_borrower_info
      return if latest_borrower_info.present?

      Rails.logger.error('Latest Borrower Info not found', class: self.class, loan_id:, borrower_id: borrower&.id)
      raise RecordNotFound, "Latest borrower info not found for loan #{loan_id}"
    end

    def validate_til_history
      return if til_history.present?

      Rails.logger.error('Til history not found', class: self.class, loan_id:)
      raise RecordNotFound, "Til history not found for loan #{loan_id}"
    end

    def validate_selected_offer
      return if loan.selected_offer.present?

      Rails.logger.error('Selected offer not found', class: self.class, loan_id:)
      raise RecordNotFound, "Selected offer not found for loan #{loan_id}"
    end

    def bank_account
      @bank_account ||= BankAccount.enabled_and_belongs_to_an_active_loan(loan.borrower).take
    end

    def lead
      @lead ||= Lead.with_code(loan.code).where(program_id: loan.program_id).take
    end

    def loan
      @loan ||= ::Loan.includes(:offers, :loan_detail).find(loan_id)
    end

    def til_history
      @til_history ||= TilHistory.where(loan_id:)
                                 .where.not(signed_at: nil).order(created_at: :desc).take
    end

    def verification_inputs
      @verification_inputs ||= VerificationInputs.find_by(loan_id:)
    end

    def underwriting_detail
      @underwriting_detail ||= UnderwritingDetail.find_by(loan_id:)
    end

    def application_attributes
      attributes = Crb::PreApprovalAttributes::Application.attr(loan).merge(
        applicant: applicant_attributes,
        policyVersions: policy_version_attributes,
        decisionFlow: decision_flow_attributes
      )
      attributes[:offers] = offer_attributes if loan.offers.present?
      attributes[:denied] = denied_attributes if declined?
      attributes
    end

    def applicant_attributes
      Crb::PreApprovalAttributes::Applicant.attr(loan, borrower, latest_borrower_info, underwriting_detail,
                                                 verification_inputs)
    end

    def policy_version_attributes
      Crb::PreApprovalAttributes::PolicyVersion.attr(loan, underwriting_detail)
    end

    def decision_flow_attributes
      Crb::PreApprovalAttributes::DecisionFlow.attr(loan, underwriting_detail)
    end

    def denied_attributes
      Crb::PreApprovalAttributes::Denied.attr(loan)
    end

    def offer_attributes
      Crb::PreApprovalAttributes::Offer.attr(loan, til_history)
    end

    def onboarded_loan?
      loan.loan_app_status.name == ::LoanAppStatus::ONBOARDED_STATUS
    end

    def front_end_declined?
      loan.loan_app_status.name == ::LoanAppStatus::FRONT_END_DECLINED_STATUS
    end

    def back_end_declined?
      loan.loan_app_status.name == ::LoanAppStatus::BACK_END_DECLINED_STATUS
    end

    def declined?
      front_end_declined? || back_end_declined?
    end

    def build_crb_pre_approval_payload
      {
        application_id: loan.id,
        application: application_attributes
      }
    end
  end
end
