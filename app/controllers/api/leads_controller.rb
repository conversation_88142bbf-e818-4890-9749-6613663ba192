# frozen_string_literal: true

module Api
  class LeadsController < ApiController
    before_action do
      Current.allowed_apps = [ExternalApp::GDS]
    end

    before_action :set_event_agent_gds
    around_action :record_request_event

    def ipl
      handle_ams_service_action
    end

    private

    def ipl_params
      {
        code: params[:code],
        phone_number: params[:Phone_number] # not a typo; this is the param name GDS sends
      }.compact_blank
    end
  end
end
