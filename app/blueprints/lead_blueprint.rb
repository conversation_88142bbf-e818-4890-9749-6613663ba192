# frozen_string_literal: true

class LeadBlueprint < Blueprinter::Base
  identifier :id

  fields :first_name,
         :last_name,
         :address,
         :city,
         :state,
         :zip_code,
         :phone_number,
         :code_used,
         :type,
         :code_status,
         :lead_bank_account,
         :service_entity_name

  field :date_of_birth do |lead|
    lead.date_of_birth&.to_time&.utc&.iso8601(3)
  end

  field :lead_bank_account do |lead|
    {
      holder_name: lead.nu_dse_holder_s_name_c,
      bank_name: lead.nu_dse_bank_name_c,
      account_number: lead.nu_dse_bank_account_number_c&.to_s,
      routing_number: lead.nu_dse_routing_number_c&.to_s,
      account_type: lead.nu_dse_account_type_c
    }
  end
end
