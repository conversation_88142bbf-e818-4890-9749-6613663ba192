# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Underwriting::InformativeResearch do
  let(:hash_data) do
    {
      'IR_Age_of_the_Oldest_Tradeline' => 3650,
      'IR_Ever_Had_Above_Loan_Indicator' => 'FALSE',
      'IR_FICO_Beacon5' => '549',
      'IR_Max_Utilization_Of_Bank_Card' => 0.65,
      'IR_NUM_BANKRUPTCY_CH_7_EVER' => 2,
      'IR_NUM_CONSUMER_DISPUTE_INDICATOR' => 3,
      'IR_Num_Credit_Cards_730D_Pre_Beyond' => 7,
      'IR_Num_DischargedPetitionedBankruptcy_Within7Years' => 1,
      'IR_Num_Inquiries_365Days' => 10,
      'IR_Num_Inquiries_60Days' => 3,
      'IR_Num_Installment_or_Revolving_3Months' => 1,
      'IR_NUM_OPEN_MORTGAGE' => 4,
      'IR_num_personal_loan_hard_inquiries_120_days' => 5,
      'IR_Num_RecentAutoDelinquency_3Months' => 0,
      'IR_Num_RecentMortgageDelinquency_3Months' => 1,
      'IR_Num_Reposession_or_Foreclosure_Last12Months' => 2,
      'IR_NUMBER_OF_PERSONAL_LOAN_LAST_180_DAYS' => 4,
      'IR_PCT_OPENED_OF_EVER_PAST_12MOS_PRE_BEYOND' => 0.75,
      'IR_Settled_trades_paid_charge_Offs_Paid_Collections_Before_DRP_Enrollment' => 10,
      'IR_Tradelines_Opened_Last_Year_Ratio' => 0.25,
      'IR_TRADES_OPENED_730_DAYS_PIL_ADJUSTED' => 6,
      'IR_Unsecured_Trades_Opened_Last_Year_Ratio' => 0.5,
      'IR_CreditFile_ResultStatusType' => 'FileReturned',
      'IR_Consumer_Deceased_Ind' => 'N'
    }
  end

  let(:subject) { described_class.new(hash_data) }

  describe '#before_drp_settled_paid_charge_off_paid_collections' do
    it 'returns the integer value of IR_Settled_trades_paid_charge_Offs_Paid_Collections_Before_DRP_Enrollment' do
      expect(subject.before_drp_settled_paid_charge_off_paid_collections).to eq(10)
    end
  end

  describe '#cc_utilization' do
    it 'returns the float value of IR_Max_Utilization_Of_Bank_Card' do
      expect(subject.cc_utilization).to eq(0.65)
    end
  end

  describe '#chapter_7_bankruptcies_ever' do
    it 'returns the integer value of IR_NUM_BANKRUPTCY_CH_7_EVER' do
      expect(subject.chapter_7_bankruptcies_ever).to eq(2)
    end
  end

  describe '#credit_cards_730_days' do
    it 'returns the integer value of IR_Num_Credit_Cards_730D_Pre_Beyond' do
      expect(subject.credit_cards_730_days).to eq(7)
    end
  end

  describe '#credit_score' do
    it 'returns the integer value of IR_FICO_Beacon5' do
      expect(subject.credit_score).to eq(549)
    end
  end

  describe '#deceased' do
    it 'returns false if IR_Consumer_Deceased_Ind is not Y' do
      expect(subject.deceased).to be false
    end
  end

  describe '#ever_had_above_loan_indicator' do
    it 'returns false if IR_Ever_Had_Above_Loan_Indicator is not TRUE' do
      expect(subject.ever_had_above_loan_indicator).to be false
    end
  end

  describe '#inquiries_120_days' do
    it 'returns the integer value of IR_num_personal_loan_hard_inquiries_120_days' do
      expect(subject.inquiries_120_days).to eq(5)
    end
  end

  describe '#inquiries_365_days' do
    it 'returns the integer value of IR_Num_Inquiries_365Days' do
      expect(subject.inquiries_365_days).to eq(10)
    end
  end

  describe '#inquiries_60_days' do
    it 'returns the integer value of IR_Num_Inquiries_60Days' do
      expect(subject.inquiries_60_days).to eq(3)
    end
  end

  describe '#installment_or_revolving_3_months' do
    it 'returns the integer value of IR_Num_Installment_or_Revolving_3Months' do
      expect(subject.installment_or_revolving_3_months).to eq(1)
    end
  end

  describe '#num_bankruptcy_7_years' do
    it 'returns the integer value of IR_Num_DischargedPetitionedBankruptcy_Within7Years' do
      expect(subject.num_bankruptcy_7_years).to eq(1)
    end
  end

  describe '#num_customer_disputes' do
    it 'returns the integer value of IR_NUM_CONSUMER_DISPUTE_INDICATOR' do
      expect(subject.num_customer_disputes).to eq(3)
    end
  end

  describe '#num_mortgage_open' do
    it 'returns the integer value of IR_NUM_OPEN_MORTGAGE' do
      expect(subject.num_mortgage_open).to eq(4)
    end
  end

  describe '#no_bureau_hit' do
    it 'returns false if IR_NoHit_Ind is not Y' do
      expect(subject.no_bureau_hit).to be false
    end
  end

  describe '#oldest_trade_line_days' do
    it 'returns the integer value of IR_Age_of_the_Oldest_Tradeline' do
      expect(subject.oldest_trade_line_days).to eq(3650)
    end
  end

  describe '#pct_opened_of_ever_past_12_months_pre_beyond' do
    it 'returns the float value of IR_PCT_OPENED_OF_EVER_PAST_12MOS_PRE_BEYOND' do
      expect(subject.pct_opened_of_ever_past_12_months_pre_beyond).to eq(0.75)
    end
  end

  describe '#personal_trades_opened_in_last_180_days' do
    it 'returns the integer value of IR_NUMBER_OF_PERSONAL_LOAN_LAST_180_DAYS' do
      expect(subject.personal_trades_opened_in_last_180_days).to eq(4)
    end
  end

  describe '#recent_auto_delinquency_3_months' do
    it 'returns the integer value of IR_Num_RecentAutoDelinquency_3Months' do
      expect(subject.recent_auto_delinquency_3_months).to eq(0)
    end
  end

  describe '#recent_mortgage_delinquency_3_months' do
    it 'returns the integer value of IR_Num_RecentMortgageDelinquency_3Months' do
      expect(subject.recent_mortgage_delinquency_3_months).to eq(1)
    end
  end

  describe '#repossession_foreclosure_last_12_months' do
    it 'returns the integer value of IR_Num_Reposession_or_Foreclosure_Last12Months' do
      expect(subject.repossession_foreclosure_last_12_months).to eq(2)
    end
  end

  describe '#trades_730_days_pil_adjusted' do
    it 'returns the integer value of IR_TRADES_OPENED_730_DAYS_PIL_ADJUSTED' do
      expect(subject.trades_730_days_pil_adjusted).to eq(6)
    end
  end

  describe '#trades_opened_last_year_ratio' do
    it 'returns the float value of IR_Tradelines_Opened_Last_Year_Ratio' do
      expect(subject.trades_opened_last_year_ratio).to eq(0.25)
    end
  end

  describe '#unsecured_trades_365_days_ratio' do
    it 'returns the float value of IR_Unsecured_Trades_Opened_Last_Year_Ratio' do
      expect(subject.unsecured_trades_365_days_ratio).to eq(0.5)
    end
  end

  describe '#address_discrepancy_flag' do
    it 'returns the boolean value of IR_Address_Discrepancy_Flag' do
      expect(subject.address_discrepancy_flag).to be true
    end
  end
end
