# frozen_string_literal: true

# == Schema Information
#
# Table name: leads
#
#  id                           :uuid             not null, primary key
#  account_number               :string(255)
#  address                      :string(255)
#  cft_account_details          :string(255)
#  cft_account_holder_name      :text
#  cft_bank_name                :text
#  city                         :string(255)
#  code                         :string(50)       not null
#  code_used                    :boolean          default(FALSE)
#  data                         :json
#  date_of_birth                :date
#  deleted_at                   :timestamptz
#  email                        :string(255)
#  expiration_date              :timestamptz
#  fico_score                   :integer
#  first_name                   :string(255)      not null
#  last_name                    :string(255)      not null
#  loan_details                 :json
#  middle_name                  :string(255)
#  months_since_enrollment      :integer
#  next_payment_date            :date
#  nu_dse_account_type_c        :string(255)
#  nu_dse_bank_account_number_c :decimal(20, )    default(0)
#  nu_dse_bank_name_c           :string(255)
#  nu_dse_holder_s_name_c       :string(255)
#  nu_dse_routing_number_c      :decimal(15, )    default(0)
#  payment_details              :json
#  personal_loan_score          :integer
#  phone_number                 :string(20)
#  project_number               :string(50)
#  service_entity_name          :string(255)      default("Beyond Finance"), not null
#  ssn                          :string(11)
#  state                        :string(2)
#  suffix_name                  :string(10)
#  total_unsecured_debt         :decimal(8, 2)
#  tradeline_details            :json
#  type                         :string(100)      not null
#  utilization_unsecured_debt   :decimal(8, 2)
#  zip_4                        :string(4)
#  zip_code                     :string(5)
#  created_at                   :timestamptz      not null
#  updated_at                   :timestamptz
#  client_id                    :string(50)
#  program_id                   :string(12)
#
# Indexes
#
#  leads_code_type_unique    (code,type) UNIQUE
#  leads_code_uppercase_idx  (upper((code)::text))
#  leads_email_index         (email)
#
require 'rails_helper'

RSpec.describe Lead, type: :model do
  let(:lead) { create(:lead) }

  describe '#with_phone_number scope' do
    let(:expected_lead) { create(:lead, phone_number: '1234567890') }
    let(:different_lead) { create(:lead) }

    it 'returns leads with the specified phone number' do
      expect(described_class.with_phone_number('1234567890')).to include(expected_lead)
      expect(described_class.with_phone_number('1234567890')).not_to include(different_lead)
    end
  end

  describe '#with_first_name scope' do
    let(:first_name) { Faker::Name.first_name }
    let(:expected_lead) { create(:lead, first_name:) }
    let(:different_lead) { create(:lead) }

    it 'returns lead from first name' do
      expect(described_class.with_first_name(first_name)).to include(expected_lead)
      expect(described_class.with_first_name(first_name)).not_to include(different_lead)
    end

    it 'returns lead from a part of first name' do
      expect(described_class.with_first_name(first_name[0, 4])).to include(expected_lead)
    end
  end

  describe '#with_last_name scope' do
    let(:last_name) { Faker::Name.last_name }
    let(:expected_lead) { create(:lead, last_name:) }
    let(:different_lead) { create(:lead) }

    it 'returns lead from first name' do
      expect(described_class.with_last_name(last_name)).to include(expected_lead)
      expect(described_class.with_last_name(last_name)).not_to include(different_lead)
    end

    it 'returns lead from a part of last name' do
      expect(described_class.with_last_name(last_name[0, 4])).to include(expected_lead)
    end
  end

  describe '#with_code scope' do
    let(:code) { 'ExGf0k' }
    let(:expected_lead) { create(:lead, code:) }
    let(:different_lead) { create(:lead) }

    it 'returns lead from code' do
      expect(described_class.with_code(code)).to include(expected_lead)
      expect(described_class.with_code(code)).not_to include(different_lead)
    end

    it 'returns lead with mixed case' do
      expect(described_class.with_code(code.downcase)).to include(expected_lead)
    end

    context 'when there are multiple leads with the same code (different case)' do
      let!(:older_lead_same_code) { create(:lead, code: expected_lead.code.downcase, created_at: 1.minute.ago) }
      let!(:newer_lead_same_code) { create(:lead, code: expected_lead.code.upcase, created_at: 1.minute.from_now) }

      it 'orders the leads by created_at descending' do
        expect(described_class.with_code(code.downcase).to_a).to eq([newer_lead_same_code, expected_lead, older_lead_same_code])
      end
    end
  end

  describe '#with_program_name scope' do
    let(:program_name) { 'BRP-12boo' }
    let(:expected_lead) { create(:lead, program_id: program_name) }
    let(:different_lead) { create(:lead) }

    it 'returns lead from program id' do
      expect(described_class.with_program_name(program_name)).to include(expected_lead)
      expect(described_class.with_program_name(program_name)).not_to include(different_lead)
    end

    it 'returns borrower from the suffix loan program id' do
      expect(described_class.with_program_name(program_name[4, 5])).to include(expected_lead)
    end
  end

  describe '#expired?' do
    it 'returns true if expiration_date is blank' do
      lead.update(expiration_date: nil)
      expect(lead.expired?).to be true
    end

    it 'returns true if current time is past expiration_date' do
      lead.update(expiration_date: 1.day.ago)
      expect(lead.expired?).to be true
    end

    it 'returns false if current time is before expiration_date' do
      lead.update(expiration_date: 1.day.from_now)
      expect(lead.expired?).to be false
    end
  end

  describe '#eligible?' do
    it 'returns false if expiration_date is blank' do
      lead.update(expiration_date: nil)
      expect(lead.eligible?).to be false
    end

    it 'returns false if current time is past expiration_date' do
      lead.update(expiration_date: 1.day.ago)
      expect(lead.eligible?).to be false
    end

    it 'returns true if current time is before expiration_date' do
      lead.update(expiration_date: 1.day.from_now)
      expect(lead.eligible?).to be true
    end
  end
end
