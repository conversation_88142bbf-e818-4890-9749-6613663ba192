# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BeyondEligibility::Decisioning, type: :model do
  describe '.table_name' do
    it 'returns the correct table name' do
      expect(BeyondEligibility::Decisioning.table_name).to eq('VW_IPL_DECISIONING_DAILY')
    end
  end

  describe 'attributes' do
    it 'has expected attributes from base class' do
      expect(BeyondEligibility::Decisioning.new).to respond_to(:program_name)
      expect(BeyondEligibility::Decisioning.new).to respond_to(:estimated_payoff_amount)
      expect(BeyondEligibility::Decisioning.new).to respond_to(:program_duration_in_tmonths)
      expect(BeyondEligibility::Decisioning.new).to respond_to(:eligibility_level)
      expect(BeyondEligibility::Decisioning.new).to respond_to(:number_of_returned_deposits_in_last_180_days)
    end
  end
end
