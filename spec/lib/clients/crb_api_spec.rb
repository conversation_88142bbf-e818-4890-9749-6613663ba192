# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::CrbApi do
  let(:config) { Rails.application.config_for(:arix) }
  let(:url) { "#{config[:api_base_url]}/preapproval/api/v2/applications/#{config.partner_schema_id}/dryrun" }
  let(:auth_base_url) { "#{config[:auth_base_url]}/connect/token" }
  let(:response_payload) { { 'applicationId' => '12345' } }
  let(:request_body) do
    {
      'issuingBank' => 'CRB',
      'product' => 'AGLE',
      'loanPurpose' => 'debt_consolidation'
    }
  end
  before do
    allow(Rails.application).to receive(:config_for).with(:crb).and_return(config)
  end

  describe '.pre_approval' do
    context 'when pre_approval call is successful' do
      before do
        stub_request(:post, auth_base_url)
          .with(
            headers: {
              'Content-Type' => 'application/x-www-form-urlencoded',
              'Accept' => 'application/json'
            }
          )
          .to_return(status: 200, body: { access_token: 'some_access_token' }.to_json, headers: { 'Content-Type' => 'application/json' })

        stub_request(:post, url)
          .with(
            body: request_body.to_json,
            headers: {
              'Authorization' => 'Bearer some_access_token',
              'Content-Type' => 'application/json',
              'Accept' => 'application/json'
            }
          )
          .to_return(status: 200, body: response_payload.to_json, headers: { 'Content-Type' => 'application/json' })

        allow(Clients::CrbApi).to receive(:authorization_header).and_return('Bearer some_access_token')
      end

      it 'returns a parsed response body' do
        result = described_class.pre_approval(request_body)
        expect(WebMock).to have_requested(:post, url).once
        expect(result).to eq(response_payload)
      end
    end

    context 'when pre_approval call throws a faraday error' do
      let(:response_hash) { { status: 400, body: 'bad request error' } }
      let(:bad_request_error) { Faraday::BadRequestError.new('bad request', response_hash) }

      before do
        stub_request(:post, url).to_raise(bad_request_error)
        allow(Clients::CrbApi).to receive(:authorization_header).and_return('Bearer some_access_token')
      end

      it 'throws an RequestFailure error' do
        expect { described_class.pre_approval(request_body) }.to raise_error(Clients::CrbApi::RequestFailure)
      end
    end

    context 'when pre_approval call returns an invalid response' do
      let(:response_payload) do
        {
          'error' => 'invalid_request',
          'error_description' => 'The request is invalid'
        }
      end

      before do
        stub_request(:post, url)
          .with(
            body: request_body.to_json,
            headers: {
              'Authorization' => 'Bearer some_access_token',
              'Content-Type' => 'application/json',
              'Accept' => 'application/json'
            }
          )
          .to_return(status: 422, body: response_payload.to_json, headers: { 'Content-Type' => 'application/json' })

        allow(Clients::CrbApi).to receive(:authorization_header).and_return('Bearer some_access_token')
      end

      it 'throws an RequestFailure error' do
        expect { described_class.pre_approval(request_body) }.to raise_error(Clients::CrbApi::RequestFailure)
      end
    end
  end

  describe '.authorization_header' do
    context 'when auth token is fetched successfully' do
      before do
        stub_request(:post, auth_base_url)
          .with(
            headers: {
              'Content-Type' => 'application/x-www-form-urlencoded',
              'Accept' => 'application/json'
            }
          )
          .to_return(status: 200, body: { access_token: 'some_access_token', token_type: 'Bearer' }.to_json, headers: { 'Content-Type' => 'application/json' })
      end

      it 'calls auth endpoint once' do
        described_class.send(:authorization_header)
        expect(WebMock).to have_requested(:post, auth_base_url).once
      end

      it 'returns the correct authorization header' do
        header = described_class.send(:authorization_header)
        expect(header).to eq('Bearer some_access_token')
      end
    end
  end
end
