# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::Leads::Ipl, type: :service do
  include_context 'service with authentication' do
    let(:token_data) { { id: external_app.id, type: Auth::VerifyOauthToken::OAUTH_TOKEN_TYPE, code: ExternalApp::GDS } }
    let(:decoded_token) { Auth::DecodedToken.new(**token_data) }

    before do
      allow(Auth::DecodeJwt).to receive(:call).and_return(decoded_token)
    end
  end

  let!(:lead) { create(:lead) }
  let(:code) { lead&.code }
  let(:phone_number) { lead&.phone_number }

  subject(:service) { described_class.new(code:, phone_number:) }
  subject(:called) { service.call }

  specify 'validations' do
    expect(service).to validate_presence_of(:code)
  end

  before do
    allow_any_instance_of(Auth::VerifyOauthToken).to receive(:call).and_return(true)
  end

  describe '.call' do
    shared_examples 'an invalid request' do
      it 'sets the status' do
        expect(called.status).to eq(status)
      end

      it 'renders the error message' do
        expect(called.body).to eq({
                                    statusCode: status,
                                    error:,
                                    message:
                                  })
      end
    end

    shared_examples 'successful request' do
      it 'sets the status' do
        expect(called.status).to eq(200)
      end

      it 'renders the body' do
        expect(called.body).to eq(body)
      end
    end

    describe 'with code and phone_number' do
      let(:body) { LeadBlueprint.render_as_hash(lead) }

      it_behaves_like 'successful request'
    end

    describe 'with only code' do
      let(:phone_number) { nil }
      let(:body) { LeadBlueprint.render_as_hash(lead) }

      it_behaves_like 'successful request'
    end

    describe 'without lead' do
      let!(:lead) { nil }
      let!(:code) { 'whatever' }
      let(:error) { 'Not Found' }
      let(:status) { 404 }
      let(:message) { "We're sorry. We're not able to recognize this code. Please double check your entry and try again." }

      it_behaves_like 'an invalid request'
    end

    describe 'with wrong code' do
      let(:code) { 'wrong' }
      let(:error) { 'Not Found' }
      let(:status) { 404 }
      let(:message) { "We're sorry. We're not able to recognize this code. Please double check your entry and try again." }

      it_behaves_like 'an invalid request'
    end

    describe 'with used code' do
      let!(:lead) { create(:lead, code_used: true) }
      let(:error) { 'Bad Request' }
      let(:status) { 400 }
      let(:message) { "We're sorry. This code has already been used." }

      it_behaves_like 'an invalid request'
    end

    describe 'with ineligible code' do
      let(:error) { 'Not Found' }
      let(:status) { 404 }
      let(:message) { "We're sorry, this code is no longer eligible." }

      context 'when the code is expired' do
        let!(:lead) { create(:lead, expiration_date: Time.now - 5.minutes) }

        it_behaves_like 'an invalid request'
      end
      context 'when the expiration_date is nil for old codes' do
        let!(:lead) { create(:lead, expiration_date: nil) }

        it_behaves_like 'an invalid request'
      end
    end

    describe 'already active loan that can not expire' do
      let!(:loan_app_status) { create(:loan_app_status, name: LoanAppStatus::NON_EXPIRABLE_STATUS.sample) }
      let!(:loan) { create(:loan, loan_app_status:, product_type: 'IPL', code:) }
      let!(:offer) { create(:offer, loan:, expiration_date: 2.weeks.from_now) }
      let!(:error) { 'Not Found' }
      let!(:status) { 404 }
      let!(:message) { 'There is an ongoing application with given invitation code' }

      it_behaves_like 'an invalid request'
    end

    describe 'loan with code that is case insensitive' do
      let!(:loan_app_status) { create(:loan_app_status, name: LoanAppStatus::NON_EXPIRABLE_STATUS.sample) }
      let!(:loan) { create(:loan, loan_app_status:, product_type: 'IPL', code:) }
      let!(:offer) { create(:offer, loan:, expiration_date: 2.weeks.from_now) }
      let!(:error) { 'Not Found' }
      let!(:status) { 404 }
      let!(:message) { 'There is an ongoing application with given invitation code' }
      subject(:service) { described_class.new(code: code.downcase) }

      it_behaves_like 'an invalid request'
    end

    describe 'already active loan with possible expired status' do
      let(:possibly_expired_status) { (LoanAppStatus::POSSIBLE_EXPIRED_STATUSES - LoanAppStatus::EXPIRED_STATUSES).sample }
      let!(:loan_app_status) { create(:loan_app_status, name: possibly_expired_status) }
      let!(:loan) { create(:loan, loan_app_status:, product_type: 'IPL', code:) }

      describe 'when offer is not expired' do
        let!(:offer) { create(:offer, loan:, expiration_date: 2.weeks.from_now) }
        let!(:error) { 'Not Found' }
        let!(:status) { 404 }
        let!(:message) { 'There is an ongoing application with given invitation code' }

        it_behaves_like 'an invalid request'
      end

      describe 'when offer is expired' do
        let!(:offer) { create(:offer, loan:, expiration_date: 1.minute.ago) }
        let(:body) { LeadBlueprint.render_as_hash(lead) }

        it_behaves_like 'successful request'
      end
    end

    describe 'loan with other status' do
      let!(:loan_app_status) { create(:loan_app_status, name: 'other') }
      let!(:loan) { create(:loan, loan_app_status:, product_type: 'IPL', code:) }
      let!(:offer) { create(:offer, loan:, expiration_date: 2.weeks.from_now) }
      let!(:body) { LeadBlueprint.render_as_hash(lead) }

      it_behaves_like 'successful request'
    end

    describe 'should be case insensitive' do
      let!(:loan_app_status) { create(:loan_app_status, name: 'other') }
      let!(:loan) { create(:loan, loan_app_status:, product_type: 'IPL', code:) }
      let!(:offer) { create(:offer, loan:, expiration_date: 2.weeks.from_now) }
      let!(:body) { LeadBlueprint.render_as_hash(lead) }
      subject(:service) { described_class.new(code: code.downcase) }

      it_behaves_like 'successful request'
    end
  end
end
