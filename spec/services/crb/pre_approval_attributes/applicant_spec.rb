# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Crb::PreApprovalAttributes::Applicant do
  let(:loan) { create(:loan, verified_dti: 25.0) }
  let!(:loan_detail) { create(:loan_detail, loan:, credit_model_score: 1) }
  let(:borrower) { create(:borrower, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', date_of_birth: Date.new(1990, 1, 1), ssn: '***********', email: '<EMAIL>') }
  let!(:latest_borrower_info) { create(:borrower_additional_info, :with_city_and_zip, loan:) }
  let!(:underwriting_detail)  { create(:underwriting_detail, loan_id: loan.id) }
  let(:verification_inputs) { create(:verification_inputs, loan_id: loan.id) }
  let!(:offer) { create(:offer, loan:, selected: true, expiration_date: 1.day.from_now) }
  let(:subject) { described_class.new(loan, borrower, latest_borrower_info, underwriting_detail, verification_inputs) }

  before do
    allow(underwriting_detail).to receive(:dti).and_return(1.0)
  end

  describe '#applicant_address_attributes' do
    it 'returns correct address attributes' do
      expect(subject.applicant_address_attributes).to eq(
        {
          type: 'physical',
          street: latest_borrower_info.address_street,
          unit: latest_borrower_info.address_apt,
          city: latest_borrower_info.city,
          state: latest_borrower_info.state,
          country: 'US',
          postalCode: latest_borrower_info.zip_code
        }.compact
      )
    end
  end

  describe '#applicant_basic_info_attributes' do
    it 'returns correct basic info attributes' do
      expect(subject.applicant_basic_info_attributes).to eq(
        {
          customerId: borrower.id,
          taxId: borrower.ssn,
          dob: borrower.date_of_birth.iso8601,
          firstName: borrower.first_name,
          lastName: borrower.last_name
        }
      )
    end
  end

  describe '#credit_underwriting_attributes' do
    it 'merges all credit underwriting attributes' do
      expect(subject.credit_underwriting_attributes).to include(:dti, :recMortDQ, :isFirstApplication)
    end
  end

  describe '#applicant_compliant_attributes' do
    it 'returns correct compliant attributes' do
      expect(subject.applicant_compliant_attributes).to eq(
        {
          ofac: 'PASS',
          cipScores: {},
          creditReportFlags: { isDeceased: false, noBureauHit: false }
        }
      )
    end
  end

  describe '#attr' do
    it 'returns the full applicant attributes hash' do
      result = subject.attr
      expect(result[:addresses].first[:street]).to eq(latest_borrower_info.address_street)
      expect(result[:phone].first[:number]).to eq(latest_borrower_info.phone_number)
      expect(result[:email].first[:email]).to eq(borrower.email)
      expect(result[:customerId]).to eq(borrower.id)
      expect(result[:firstName]).to eq(borrower.first_name)
      expect(result[:compliance]).to eq(subject.applicant_compliant_attributes)
    end

    it 'return credit_underwriting_personal_info_attributes in creditUnderwriting' do
      result = subject.attr
      expect(result[:credit][:underwriting][:dti]).to eq(underwriting_detail.dti)
      expect(result[:credit][:underwriting][:numNSFLast1Mo]).to eq(verification_inputs.nsfs_30_days)
    end
  end
end
