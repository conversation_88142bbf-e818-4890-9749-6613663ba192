# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Onboarding::ProcessSignedContract, type: :service do
  include ActiveSupport::Testing::TimeHelpers

  let(:service_object) { described_class.new(**params) }

  let(:loan) { create(:loan, :approved) }
  let(:til_history) { create(:til_history, loan:, docusign_webhook_id:) }

  let(:docusign_webhook_id) { SecureRandom.uuid }
  let(:loan_agreement_filename) { "CRB Installment Loan Agreement_version_#{loan_agreement_version}_ERICA_LAMBERT_#{SecureRandom.uuid}.pdf" }
  let(:loan_agreement_template) { DocTemplate::TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT] }
  let(:loan_agreement_version) { rand(20..30).to_s }
  let(:csc_filename) { "#{csc_template}_version_#{rand(5..15)}_ERICA_LAMBERT_#{SecureRandom.uuid}.pdf" }
  let(:csc_template) { DocTemplate::TYPES[:CREDIT_SERVICES_CONTRACT_MARYLAND] }
  let(:noc_filename) { "#{noc_template}_version_#{rand(5..15)}_ERICA_LAMBERT_#{SecureRandom.uuid}.pdf" }
  let(:noc_template) { DocTemplate::TYPES[:NOTICE_OF_CANCELLATION_MARYLAND] }
  let(:noc2_filename) { "#{noc_template}_2_version_#{rand(5..15)}_ERICA_LAMBERT_#{SecureRandom.uuid}.pdf" }

  let(:ip_address) { Faker::Internet.ip_v4_address }
  let(:docusign_account_id) { SecureRandom.uuid }
  let(:docusign_api_mock) { instance_double(Clients::DocusignApi, account_id: docusign_account_id, get_signed_client_ip_address: ip_address) }
  let(:params) do
    {
      loan_id: loan.id,
      docusign_webhook_id:,
      loan_agreement_filename:,
      loan_agreement_template:,
      loan_agreement_version:,
      csc_filename:,
      csc_template:,
      noc_filename:,
      noc_template:,
      noc2_filename:
    }
  end

  before do
    create(:offer, loan:, selected: true)
    create(:bank_account, loan:, borrower: loan.borrower)
    create(:loanpro_loan, loan:, loanpro_loan_id: til_history.loanpro_loan_external_id)
    allow(Onboarding::LoanproOnboarding).to receive(:call).with(loan:, til_history:)
    allow(Onboarding::SyncEligibilityData).to receive(:call).with(loan_id: loan.id)
    allow(Clients::DocusignApi).to receive(:new).and_return(docusign_api_mock)
    allow(Onboarding::RecordSignedContractAndOnboardLoanJob).to receive(:perform_async)
    allow(Loans::SocureMonitoringJob).to receive(:perform_async).with(loan.id, 'enable')
    allow(Plaid::RegisterItemWebhookJob).to receive(:perform_async)
    allow(Crb::LoanPreApprovalJob).to receive(:perform_async)
  end

  describe '#call' do
    it 'succeeds for APPROVED' do
      loan.update!(loan_app_status: LoanAppStatus.for(:approved))
      loan.loanpro_loan.update(til_sign_date: Time.now)
      service_object.call
    end

    it 'succeeds for INITIAL_TIL_SUBMIT' do
      loan.update!(loan_app_status: LoanAppStatus.for(:initial_til_submit))
      service_object.call
    end

    it 'sets the til_sign_date value on the associated LoanproLoan record' do
      travel_to Time.zone.now do
        service_object.call
        expect(LoanproLoan.find_by(loan_id: loan.id).til_sign_date).to be_within(1.second).of(Time.zone.now)
      end
    end

    it 'updates the loan to the INITIAL_TIL_SUBMIT status' do
      service_object.call
      expect(loan.reload.loan_app_status.name).to eq('INITIAL_TIL_SUBMIT')
    end

    it 'calls the SyncEligibilityData service with the required arguments' do
      service_object.call
      expect(Onboarding::SyncEligibilityData).to have_received(:call).with(loan_id: loan.id)
    end

    it 'triggers the LoanPro onboarding service' do
      service_object.call
      expect(Onboarding::LoanproOnboarding).to have_received(:call)
    end

    it 'sets the signed_at value on the associated TilHistory record' do
      travel_to Time.zone.now do
        service_object.call
        expect(til_history.reload.signed_at).to be_within(1.second).of(Time.zone.now)
      end
    end

    it 'registers the Plaid item webhook' do
      service_object.call
      loan.bank_accounts.each do |bank_account|
        expect(Plaid::RegisterItemWebhookJob).to have_received(:perform_async).with(bank_account.id)
      end
    end

    it 'triggers the RecordSignedContractAndOnboardLoanJob' do
      service_object.call

      expect(Onboarding::RecordSignedContractAndOnboardLoanJob).to have_received(:perform_async).with(
        loan.id,
        ip_address,
        loan_agreement_filename,
        loan_agreement_template,
        loan_agreement_version,
        docusign_webhook_id,
        csc_filename,
        csc_template,
        noc_filename,
        noc_template,
        noc2_filename
      )
    end

    context 'when loan type is ipl' do
      let(:loan) { create(:ipl_loan, :approved) }
      it 'calls crb loan pre approval job' do
        service_object.call
        expect(Crb::LoanPreApprovalJob).to have_received(:perform_async)
      end
    end

    context 'when loan type is not ipl' do
      let(:loan) { create(:upl_loan, :approved) }
      it 'does not call crb loan pre approval job' do
        service_object.call
        expect(Crb::LoanPreApprovalJob).not_to have_received(:perform_async)
      end
    end

    context 'when an error is encountered' do
      before do
        allow(ExceptionLogger).to receive(:warn)
      end

      it 'reports an error when the specified loan is not found' do
        params[:loan_id] = SecureRandom.uuid

        expected_message = "DocuSign webhook - Cannot find loan with id #{params[:loan_id]}"

        expect { service_object.call }.to raise_error(Onboarding::ProcessSignedContract::LoanRecordError, expected_message)
      end

      it 'reports an error when the loan has no offers' do
        Offer.where(loan:).destroy_all

        expected_message = "You cannot perform this action, loan's status is incorrect"

        expect { service_object.call }.to raise_error(Onboarding::ProcessSignedContract::LoanStatusError, expected_message)
      end

      it 'reports an error when all offers on the loan are expired' do
        Offer.where(loan:).update(expiration_date: 1.day.ago)

        expected_message = "You cannot perform this action, loan's status is incorrect"

        expect { service_object.call }.to raise_error(Onboarding::ProcessSignedContract::LoanStatusError, expected_message)
      end

      it 'reports an error when the loan is not in the approved or intial_til_submit status' do
        status_name = (LoanAppStatus::ID_TO_NAME - %w[NONE APPROVED INITIAL_TIL_SUBMIT ONBOARDED]).sample
        loan.update!(loan_app_status: LoanAppStatus.for(status_name))

        expected_message = "You cannot perform this action, loan's status is incorrect"

        expect { service_object.call }.to raise_error(Onboarding::ProcessSignedContract::LoanStatusError, expected_message)
      end

      it 'reports an error when no LoanproLoan record has been created for the loan' do
        LoanproLoan.where(loan:).destroy_all

        expected_message = "DocuSign webhook - Cannot find loanPro loan associated to id #{loan.id} " \
                           "and loanpro_loan_id #{til_history.loanpro_loan_external_id}"

        expect { service_object.call }.to raise_error(Onboarding::ProcessSignedContract::LoanRecordError, expected_message)
      end

      it 'reports an error when the LoanproLoan record has expired for the loan' do
        LoanproLoan.where(loan:).update(created_at: 25.hours.ago)

        expected_message = 'You cannot perform this action, loanpro_loan ' \
                           "#{til_history.loanpro_loan_external_id} has expired"

        expect { service_object.call }.to raise_error(Onboarding::ProcessSignedContract::LoanStatusError, expected_message)
      end
    end

    context 'when loan is IPL' do
      before do
        loan.update!(product_type: Loan::IPL_LOAN_PRODUCT_TYPE)
      end

      it 'triggers the Crb::LoanPreApprovalJob' do
        service_object.call

        expect(Crb::LoanPreApprovalJob).to have_received(:perform_async)
      end
    end
  end
end
