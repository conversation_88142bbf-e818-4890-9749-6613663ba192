# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LeadBlueprint, type: :blueprint do
  let(:lead) { build(:lead) }
  describe 'normal' do
    subject { described_class.render_as_hash(lead) }
    let(:expected_attributes) do
      {
        id: lead.id,
        first_name: lead.first_name,
        last_name: lead.last_name,
        address: lead.address,
        city: lead.city,
        state: lead.state,
        zip_code: lead.zip_code,
        phone_number: lead.phone_number,
        code_used: lead.code_used,
        type: lead.type,
        date_of_birth: lead.date_of_birth&.strftime('%Y-%m-%d'),
        code_status: 'valid',
        service_entity_name: 'Beyond Finance',
        lead_bank_account: {
          holder_name: lead.nu_dse_holder_s_name_c,
          bank_name: lead.nu_dse_bank_name_c,
          account_number: lead.nu_dse_bank_account_number_c&.to_s,
          routing_number: lead.nu_dse_routing_number_c&.to_s,
          account_type: lead.nu_dse_account_type_c
        }
      }
    end

    it 'renders the proper attributes' do
      expect(subject).to eq(expected_attributes)
    end
  end

  let(:lead_with_nil_values) do
    build(:lead,
          nu_dse_holder_s_name_c: nil,
          nu_dse_bank_name_c: nil,
          nu_dse_bank_account_number_c: nil,
          nu_dse_routing_number_c: nil,
          nu_dse_account_type_c: nil)
  end
  describe 'can handle nil lead_bank_account values' do
    subject { described_class.render_as_hash(lead_with_nil_values) }
    let(:expected_attributes) do
      {
        id: lead_with_nil_values.id,
        first_name: lead_with_nil_values.first_name,
        last_name: lead_with_nil_values.last_name,
        address: lead_with_nil_values.address,
        city: lead_with_nil_values.city,
        state: lead_with_nil_values.state,
        zip_code: lead_with_nil_values.zip_code,
        phone_number: lead_with_nil_values.phone_number,
        code_used: lead_with_nil_values.code_used,
        type: lead_with_nil_values.type,
        date_of_birth: lead_with_nil_values.date_of_birth&.strftime('%Y-%m-%d'),
        code_status: 'valid',
        service_entity_name: 'Beyond Finance',
        lead_bank_account: {
          holder_name: nil,
          bank_name: nil,
          account_number: nil,
          routing_number: nil,
          account_type: nil
        }
      }
    end

    it 'renders the proper attributes' do
      expect(subject).to eq(expected_attributes)
    end
  end
end
