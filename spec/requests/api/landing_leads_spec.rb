# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Landing Leads requests', type: :request do
  include ActiveSupport::Testing::TimeHelpers

  before do
    allow(Rails.logger).to receive(:error)
  end

  path '/api/landing_leads' do
    post 'Creates a landing lead' do
      include_context 'document example'

      description 'Save lead details coming from landing page'
      produces 'application/json'
      consumes 'application/json'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          first_name: {
            type: :string,
            example: '<PERSON>'
          },
          last_name: {
            type: :string,
            example: 'Doe'
          },
          phone_number: {
            type: :string,
            example: '1234567890'
          },
          email: {
            type: :string,
            example: '<EMAIL>'
          },
          privacy_accepted: {
            type: :boolean,
            example: 'true'
          },
          tcpa_accepted: {
            type: :boolean,
            example: 'true'
          },
          url: {
            type: :string,
            description: 'URL of the front-end page the customer visited (path and query params)',
            example: '/graduation-loan?s=bf&offer=N5SQsg&utm_source=email&utm_content=day1_email'
          }
        },
        required: %i[url]
      }

      before do
        travel_to('2024-02-01T12:00:00'.to_datetime)
      end

      let!(:code) { 'Wv1F5Q' }
      let(:last_name) { Faker::Name.last_name }
      let!(:lead) { create(:lead, code:, email:, last_name:) }
      let(:first_name) { Faker::Name.first_name }
      let(:last_name) { Faker::Name.last_name }
      let(:phone_number) { Faker::Number.numerify("#{rand(2..9)}#########") }
      let(:email) { Faker::Internet.email }
      let(:identity_id) { SecureRandom.uuid }
      let(:user) { create(:user, email:) }

      response '201', 'Successful' do
        let(:payload) do
          {
            first_name:,
            last_name:,
            phone_number:,
            email:,
            url: "/graduation-loan?s=bf&offer=#{code}&utm_source=email&utm_content=day1_email",
            tcpa_accepted: true
          }
        end

        run_test! do |response|
          lead = LandingLead.find(response.json_body['id'])
          expect(lead.first_name).to eq(first_name)
          expect(lead.last_name).to eq(last_name)
          expect(lead.phone_number).to eq(phone_number)
          expect(lead.email).to eq(email)
          expect(lead.url).to eq(payload[:url])
          expect(lead.lead_code).to eq(code)
          expect(lead.tcpa_accepted_at).to eq('2024-02-01T12:00:00')
          expect(lead.service_entity_name).to eq('Beyond Finance')
          expect_request_event_record
        end

        context 'when the landing page url is invalid' do
          before do
            payload[:url] = '@@not-a-url@#$!@'
          end

          # it saves the record with nil lead_code and service_entity_name
          run_test! do |response|
            lead = LandingLead.find(response.json_body['id'])
            expect(lead.url).to eq(payload[:url])
            expect(lead.lead_code).to be_nil
            expect(lead.service_entity_name).to be_nil
            expect_request_event_record
          end
        end

        context 'when only url, email, and privacy acceptance are specified' do
          before do
            payload.clear
            payload[:privacy_accepted] = true
            payload[:email] = '<EMAIL>'
            payload[:url] = '/graduation-loan?s=bf&offer=Wv1F5Q&utm_source=email&utm_content=day1_email'
          end

          run_test! do |response|
            lead = LandingLead.find(response.json_body['id'])
            expect(lead.tcpa_accepted_at).to be_nil
            expect(lead.first_name).to eq(payload[:first_name])
            expect(lead.last_name).to eq(payload[:last_name])
            expect(lead.phone_number).to eq(payload[:phone_number])

            expect(lead.url).to eq(payload[:url])
            expect(lead.email).to eq(payload[:email])
            expect(lead.privacy_accepted_at).to eq('2024-02-01T12:00:00')
          end
        end

        context 'when the landing page url is missing expected params' do
          before do
            payload[:url] = '/graduation-loan?utm_source=email&utm_content=day1_email'
          end

          # it saves the record with nil lead_code and service_entity_name
          run_test! do |response|
            lead = LandingLead.find(response.json_body['id'])
            expect(lead.url).to eq(payload[:url])
            expect(lead.lead_code).to be_nil
            expect(lead.service_entity_name).to be_nil
          end
        end

        context 'when no borrower or loan exists for the lead' do
          let!(:lead) { create(:lead, code:, email:, last_name:) }

          it 'includes account-lead data in the create response' do
            post('/api/landing_leads', params: payload)

            expect(response.json_body).to match(
              hash_including(
                'lead' => include(
                  'code_status' => 'valid'
                )
              )
            )
          end

          it 'matches differently-cased codes' do
            lead.update!(code: code.downcase)

            post('/api/landing_leads', params: payload)

            expect(response.json_body).to match(
              hash_including(
                'lead' => include(
                  'code_status' => 'valid'
                )
              )
            )
          end
        end

        context 'when a borrower exists and the lead is valid' do
          let!(:loan_app_status) { create(:loan_app_status, :approved) }
          let(:expired_loan_app_status) { create(:loan_app_status, :expired) }
          let!(:borrower) { create(:borrower, email:, first_name:, last_name:, identity_id:) }
          let!(:loan) { create(:loan, borrower:, code:, loan_app_status:) }
          let!(:lead) { create(:lead, code:, email:, last_name:) }

          before { user.update!(activated_account: true, activated_at: Time.zone.now) }

          it 'includes all account data in the create response' do
            post('/api/landing_leads', params: payload)

            expect(response.json_body).to match(
              hash_including(
                'id' => response.json_body['id']
              )
            )

            expect(response.json_body).to match(
              hash_including(
                'borrower' => include(
                  'id' => borrower.id,
                  'identity_id' => identity_id,
                  'email' => email,
                  'first_name' => first_name,
                  'last_name' => last_name,
                  'activated_account' => true
                )
              )
            )

            expect(response.json_body).to match(
              hash_including(
                'loan' => include(
                  'id' => loan.id,
                  'product_type' => loan.product_type,
                  'loan_app_status' => loan_app_status.name,
                  'is_active' => true
                )
              )
            )

            expect(response.json_body).to match(
              hash_including(
                'lead' => include(
                  'code_status' => 'valid'
                )
              )
            )

            expect(response.json_body['email_verdict']).to be_blank
          end

          it 'does not have a loan when the latest loan is expired' do
            loan.update(loan_app_status: expired_loan_app_status)
            post('/api/landing_leads', params: payload)

            expect(response.json_body['loan']).to be_nil
          end
        end

        context 'withdraws loans' do
          let!(:loan_app_status) { create(:loan_app_status, :basic_info_complete) }
          let(:add_info_complete_loan_app_status) { create(:loan_app_status, :add_info_complete) }
          let!(:borrower) { create(:borrower, email:, first_name:, last_name:, identity_id:) }
          let!(:loan) { create(:loan, borrower:, code:, loan_app_status:) }
          let!(:lead) { create(:lead, code:, email:, last_name:) }

          it 'calls the withdraw job for basic_info_complete' do
            expect(Loans::WithdrawJob).to receive(:perform_async)
              .with(loan.id)

            post('/api/landing_leads', params: payload)
            expect_request_event_record
          end

          context 'when account is activated' do
            before { user.update!(activated_account: true, activated_at: Time.zone.now) }

            it 'does not call withdraw job' do
              expect(Loans::WithdrawJob).not_to receive(:perform_async)

              post('/api/landing_leads', params: payload)
            end
          end

          it 'calls the withdraw job for add_info_complete status' do
            loan.update(loan_app_status: add_info_complete_loan_app_status)
            expect(Loans::WithdrawJob).to receive(:perform_async)
              .with(loan.id)

            post('/api/landing_leads', params: payload)
          end

          it 'does not call withdraw job when OFFERED' do
            offered_status = LoanAppStatus.for('OFFERED')
            loan.update(loan_app_status: offered_status)

            expect(Loans::WithdrawJob).not_to receive(:perform_async)

            post('/api/landing_leads', params: payload)
          end

          it 'does not not withdraw approved loans' do
            loan.update(loan_app_status: LoanAppStatus.for('APPROVED'))

            expect(Loans::WithdrawJob).not_to receive(:perform_async)

            post('/api/landing_leads', params: payload)
          end
        end
      end

      response '400', 'Bad request' do
        let(:payload) { {} }

        run_test! do |response|
          expect(response.json_body['errors']).not_to be_empty
        end

        context 'when the url is not sent' do
          let(:payload) do
            {
              email: Faker::Internet.email,
              url: nil
            }
          end

          # it does NOT save the record
          run_test! do |response|
            expect(response.json_body['errors']).to eq({ 'url' => ['is required'] })
            expect(LandingLead.find_by(email: payload[:email])).to be_blank
            expect_request_event_record
          end
        end
      end

      response '428', 'Precondition Required', with_feature_switch_on: :email_validation do
        let(:email_validate_double) { double(valid?: false, verdict: 'Invalid', score: 0.43) }
        let(:payload) do
          {
            email:,
            url: "/graduation-loan?s=bf&offer=#{code}&utm_source=email&utm_content=day1_email",
            tcpa_accepted: true
          }
        end
        before do
          allow(Sendgrid::EmailValidate).to receive(:call).and_return(email_validate_double)
        end

        run_test! do |response|
          expect(response.status).to eq(428)

          expect(response.json_body['email_verdict']).to eq('Invalid')

          expect(Sendgrid::EmailValidate).to have_received(:call)
            .with(email:, source: 'intake lander')
        end
      end
    end
  end
end
