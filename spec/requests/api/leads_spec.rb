# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Leads', type: :request do
  include ServiceObjectHelper

  path '/api/leads/ipl/{code}' do
    get 'Find lead and start loan application' do
      include_context 'document example'
      include_context 'request with authentication' do
        let(:token_data) { { id: external_app.id, type: Auth::VerifyOauthToken::OAUTH_TOKEN_TYPE, appType: ExternalApp::GDS } }
      end

      description 'Called by GDS to look up a lead by offer code and phone number'
      tags 'IPL'
      operationId 'startLoanApplication'
      consumes 'application/json'
      produces 'application/json'

      parameter name: :code, in: :path, type: :string, description: 'offer code'
      parameter name: :Phone_number, in: :query, type: :string, description: 'phone number'

      let!(:lead) { create(:lead, type: 'IPL') }
      let(:code) { lead.code }
      let(:Phone_number) { lead.phone_number } # swagger expects the `let` to match the actual parameter name
      let(:query_string) { "Phone_number=#{Phone_number}" }
      let(:path) { "/api/leads/ipl/#{code}" }
      let(:http_method) { :get }

      response 200, 'Successful response' do
        describe 'when the phone number param is present' do
          let!(:response_body) do
            LeadBlueprint.render_as_hash(lead)
                         .deep_stringify_keys
          end

          it_behaves_like 'successful ams primary request' do
            it 'records an event_record' do
              expect_request_event_record
            end
          end
        end

        describe 'when the phone number param is not present' do
          let(:Phone_number) { nil }
          let!(:response_body) do
            LeadBlueprint.render_as_hash(lead)
                         .deep_stringify_keys
          end

          it_behaves_like 'successful ams primary request'
        end

        describe 'when another lead exists with same code (different case) and different phone number' do
          let!(:lead_with_same_code) do
            create(:lead, type: 'IPL',
                          code: lead.code.downcase,
                          created_at: lead.created_at + 1.minute) # newer created_at makes Lead#with_code return it first so this test fails unless the lookup includes phone number
          end
          let!(:response_body) do
            LeadBlueprint.render_as_hash(lead) # the expected lead should be found based on phone number
                         .deep_stringify_keys
          end

          it_behaves_like 'successful ams primary request'
        end
      end

      response 404, 'Not found' do
        let(:response_status) { 404 }
        let(:Phone_number) { nil }

        describe 'no lead found' do
          let(:code) { 'something' }
          let(:response_body) do
            not_found_response("We're sorry. We're not able to recognize this code. Please double check your entry and try again.").stringify_keys
          end

          it_behaves_like 'successful ams primary request' do
            it 'records an event_record' do
              expect_request_event_record
            end
          end
        end

        describe 'lead present, but an active loan already used the code' do
          let(:loan_app_status) { create(:loan_app_status, name: LoanAppStatus::ONGOING_LOAN_STATUSES.sample) }
          let(:loan) { create(:loan, code: lead.code, product_type: 'IPL', loan_app_status:) }
          let!(:offer) { create(:offer, expiration_date: 2.weeks.from_now, loan:) }
          let(:response_body) do
            not_found_response('There is an ongoing application with given invitation code').stringify_keys
          end
        end

        describe 'lead present, but code is not eligible' do
          let!(:lead) { create(:lead, expiration_date: Time.now - 2.minutes) }
          let(:response_body) do
            not_found_response("We're sorry, this code is no longer eligible.").stringify_keys
          end

          it_behaves_like 'successful ams primary request'
        end
      end

      response 400, 'Bad request' do
        let(:response_status) { 400 }
        describe 'lead present, but code is already used' do
          let!(:lead) { create(:lead, code_used: true) }
          let(:response_body) do
            bad_request_response("We're sorry. This code has already been used.").stringify_keys
          end

          it_behaves_like 'successful ams primary request' do
            it 'has a request event record' do
              expect_request_event_record
            end
          end
        end
      end

      it_behaves_like 'an authenticated route'
    end
  end
end
