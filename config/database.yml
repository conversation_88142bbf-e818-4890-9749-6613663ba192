default: &default
  primary: &primary
    adapter: postgresql
    host: <%= ENV.fetch('DATABASE_HOST', 'localhost') %>
    port: 5432
    username: abovelending
    password: <%= ENV['ABOVELENDING_DATABASE_PASSWORD'] %>

development:
  primary:
    <<: *primary
    database: <%= ENV.fetch('ALP_DATABASE_NAME', 'abovelending') %>

test:
  primary:
    <<: *primary
    # TEST_ENV_NUMBER is ingested by parallel_tests allowing running tests in parallel without conflicts
    database: abovelending_test<%= ENV['TEST_ENV_NUMBER'] ? "_#{ENV['TEST_ENV_NUMBER']}" : '' %>

sandbox:
  primary:
    <<: *primary
    host: sandboxe1.cluster-ckgcfcuurvms.us-east-1.rds.amazonaws.com
    database: abovelending

staging:
  primary:
    <<: *primary
    host: stagee1.cluster-ckgcfcuurvms.us-east-1.rds.amazonaws.com
    database: abovelending-stage

production:
  primary:
    <<: *primary
    host: prode1.cluster-ckgcfcuurvms.us-east-1.rds.amazonaws.com
    database: abovelending
    username: ams_above_bot
    pool: 10
