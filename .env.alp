PIDFILE=/tmp/pids/server.pid

# Enable local webhooks simulation in development (not applicable to lower environments).
#
# When enabled, simulates webhooks for specific actions.
# Currently supports creating offers when
#   Clients::GdsApi.get_offers is invoked.
ENABLE_LOCAL_WEBHOOKS=true

# Database
DATABASE_HOST=host.docker.internal
ABOVELENDING_DATABASE_PASSWORD=abovedev

# Frontend development
VITE_RUBY_HOST=vite

# Beyond API is mocked on Postman's Mocked Servers
BEYOND_LENDING_API_HOST=https://aa9d82dc-d865-4621-ac91-5363231e40c3.mock.pstmn.io
BEYOND_LENDING_NEW_API_HOST=https://aa9d82dc-d865-4621-ac91-5363231e40c3.mock.pstmn.io
COMMUNICATIONS_SERVICE_BASE_URL=http://host.docker.internal:3001
PORT=3002
RAILS_LOG_TO_STDOUT=true
RAILS_SERVE_STATIC_FILES=true
RAILS_SKIP_ASSET_COMPILATION=true
RAILS_SKIP_MIGRATIONS=false
REDIS_PREFIX=ams
REDIS_URI=redis://redis:6379/0
REDIS_URL=redis://redis:6379/1
SECRET_KEY_BASE=true
SIDEKIQ_DASHBOARD_USERNAME='admin'
SIDEKIQ_DASHBOARD_PASSWORD='password'
GDS_BASE_URL=https://0b31302d-4216-44da-b7a0-88f522e77115.mock.pstmn.io
GDS_AUTH_BASE_URL=https://0b31302d-4216-44da-b7a0-88f522e77115.mock.pstmn.io
GDS_CLIENT_ID=
GDS_SECRET=
# Sandbox Path is nil and staging/production can be commented out for the
# default /transaction
# GDS_PATH=
BUNDLE_GEMS__CONTRIBSYS__COM="${BUNDLE_GEMS__CONTRIBSYS__COM}"
SERVICE_LAYER_BASE_URL=http://host.docker.internal:3000
SERVICE_LAYER_V2_BASE_URL=http://host.docker.internal:3004
FLIPPER_UI_SECRET=flipper
DISABLE_FARADAY_LOGGING=false
USE_MINIO_FAKE_S3=true
AMS_DATA_S3_BUCKET="above-lending-ams-data-sandbox"
AWS_ACCESS_KEY_ID=aboveUser
AWS_SECRET_ACCESS_KEY=password
AWS_REGION=us-east-1
CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME=above-lending-documents-dev
CONTRACT_DOCUMENTS_SECONDARY_BACKUP_S3_BUCKET_NAME=above-lending-documents-secondary-sandbox
USE_MINIO_FAKE_S3=true
JWT_DOCUSIGN_EXT_APP_ID=docusign-external-app-id
LANDER_BASE_URL=http://localhost:3000
LOANPRO_TOKEN=
LOANPRO_INSTANCE_ID=
LOANPRO_PCIWALLET_SECRET=test-pci-wallet-secret
LOANPRO_PCIWALLET_TOKEN=test-pci-wallet-token

# Dash
DASH_API_BASE_URL=http://dash-test.com/api/
DASH_ACCESS_TOKEN = access_token
UPL_DASH_FUNDING_ENABLED=true

# DocuSign
DOCUSIGN_BASE_URL=https://demo.docusign.net/restapi

## Direct License Applications
# DOCUSIGN_ACCOUNT_ID=
# DOCUSIGN_IMPERSONATED_USER_GUID=
# DOCUSIGN_INTEGRATOR_CLIENT_ID=
# DOCUSIGN_PEM=

## CRB Applications
# DOCUSIGN_CRB_ACCOUNT_ID=
# DOCUSIGN_CRB_IMPERSONATED_USER_GUID=
# DOCUSIGN_CRB_INTEGRATOR_CLIENT_ID=
# DOCUSIGN_CRB_PEM=

## SendGrid
SENDGRID_VALIDATION_API_KEY=

# Trust Pilot: Defaults to get tests passing, Update .env.alp.override for
# development mode
TRUSTPILOT_BUSINESS_UNIT_ID=test-business-unit-id
TRUSTPILOT_AUTH_TOKEN=test-trust-pilot-auth-token
TRUSTPILOT_IPL_TEMPLATE_ID=test-template-id
TRUSTPILOT_API_KEY=test-trust-pilot-api-key
TRUSTPILOT_SENDER_EMAIL=<EMAIL>
TRUSTPILOT_ACCOUNT_ID=<EMAIL>
TRUSTPILOT_ACCOUNT_PASSWORD=test-trust-pilot-password
TRUSTPILOT_REPLY_TO=<EMAIL>
TRUSTPILOT_REDIRECT_URI=https://test.abovelending.com
TRUSTPILOT_LOCALE=en-US
TRUSTPILOT_TOKEN_URL=https://test-api.trustpilot.com/v1/oauth/oauth-business-users-for-applications
TRUSTPILOT_INVITATION_URL=https://test-invitations-api.trustpilot.com/v1/private/business-units

# Talkdesk: There's no test environment, so we have separate Landing Page Leads record lists
# set up for sandbox, staging, and production in Talkdesk
TALKDESK_AUTH_BASE_URL=https://abovelending.talkdeskid.com
TALKDESK_BASE_URL=https://api.talkdeskapp.com
TALKDESK_CLIENT_ID=094c296d01d3439ab813cc0d418876f7
# Set this in .env.alp.override to make calls to Talkdesk in dev environment
TALKDESK_SECRET=test-talkdesk-secret
# These point to the sandbox lists
TALKDESK_DROPOFF_LIST_MAP={"Beyond Finance":"657bc70c-2db6-4d74-82c6-505533837c80","Five Lakes Law Group":"c2392353-59d8-43cc-a1a1-c5d6393ee4d9"}
TALKDESK_POST_OFFER_DROPOFF_LIST_MAP={"Beyond Finance":"f84a73b4-9279-4c8c-aa00-5bf9732570b0","Five Lakes Law Group":"b90efb73-bd47-40c2-8f84-4b974373e89c"}

# Snowflake SQL REST API
SNOWFLAKE_BASE_URL=https://bha08429.us-east-1.snowflakecomputing.com
SNOWFLAKE_TIMEOUT=60
SNOWFLAKE_ACCOUNT=BHA08429
SNOWFLAKE_USER=AMS_TEST
SNOWFLAKE_DATABASE=API
SNOWFLAKE_SCHEMA=AMS_TEST
SNOWFLAKE_WAREHOUSE=API_WH_XSMALL
SNOWFLAKE_ROLE=AMS_TEST
# This is a dummy key that doesn't actually have access to Snowflake. If you need to make actual
# Snowflake calls in development, the private key for the AMS_TEST user can be found in bitwarden
# entry 'AMS - Snowflake Env Vars (sandbox/staging/dev)'
SNOWFLAKE_PRIVATE_KEY="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

PLAID_BASE_URL=https://sandbox.plaid.com
PLAID_CLIENT_ID=plaidClientId
PLAID_SECRET=plaidClientSecret

# Arix API
ARIX_API_BASE_URL=https://arixapisandbox.crbnj.net
ARIX_AUTH_BASE_URL=https://oauthtest.crbnj.net
ARIX_CLIENT_ID=testClient
ARIX_CLIENT_SECRET=test-secret

# Ocrolus API
OCROLUS_AUTH_BASE_URL=https://auth.ocrolus.com
OCROLUS_BASE_URL=https://api.ocrolus.com
OCROLUS_CLIENT_ID=toyVadrqqSwDG04bt7u2fIPKxR0MtN74
# Set this in .env.alp.override to make calls to Ocrolus in dev environment
OCROLUS_SECRET=ocrolus-secret
OCROLUS_WEBHOOK_USERNAME=test-ocrolus-webhook-username
OCROLUS_WEBHOOK_PASSWORD=test-ocrolus-webhook-password

# PostGrid API
POSTGRID_BASE_URL=https://api.postgrid.com
POSTGRID_API_KEY=test-postgrid-api-key

# Devise
DEVISE_SECRET_KEY=22bb9fd63b2580cc8f490de91bae0f33c728430a7c99ae31387721b4575ab986f1a95408114013e1da4de6284d8ef449e71a9cd78f92c3f3ca9a81bfad862613

# Socure API
SOCURE_BASE_URL=https://sandbox.socure.com/api/3.0/
SOCURE_API_KEY=23531c96-8f26-40b1-b9aa-cde5cd53b876
SOCURE_WEBHOOK_TOKEN=0vYiDpUKaLmqXMCA6vA2z0v1huNfSwQts0M7KSPNB7M=
